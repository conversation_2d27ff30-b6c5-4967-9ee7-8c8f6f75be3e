{"name": "cpesc-frontend", "private": true, "version": "2.0.0", "type": "module", "scripts": {"start": "npm run start:dev", "start:dev": "npm run copy-env:des && vite", "start:hom": "npm run copy-env:hom && vite", "build": "tsc -b && vite build", "build:dev": "npm run copy-env:des && npm run build", "build:hom": "npm run copy-env:hom && npm run build", "copy-env:des": "cp src/env.dev.ts src/env.ts", "copy-env:hom": "cp src/env.hom.ts src/env.ts", "preview": "vite preview"}, "dependencies": {"@react-pdf/renderer": "^4.3.0", "@tanstack/react-query": "^5.83.0", "classnames": "^2.5.1", "framer-motion": "^12.23.5", "js-cookie": "^3.0.5", "primeflex": "^4.0.0", "primeicons": "^7.0.0", "primereact": "^10.9.6", "qrcode": "^1.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-number-format": "^5.4.4", "react-router-dom": "^7.6.3", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@types/express-serve-static-core": "^5.0.7", "@types/js-cookie": "^3.0.6", "@types/qrcode": "^1.5.5", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "sass": "^1.89.2", "typescript": "^5.8.3", "vite": "^7.0.4", "vite-plugin-checker": "^0.9.3"}}