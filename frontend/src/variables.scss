// Variável SCSS pois é utilizado em media queries
$limite-largura-tela-mobile: 512px;
$limite-largura-tela-media: 1024px;
$limite-largura-tela-padrao: 1280px;
$limite-largura-tela-grande: 1536px;

/**
 * Palheta de cores do sistema
 */
:root {
  --cor-fundo-padrao: hsl(45, 80%, 98%);
  --cor-texto-destacado: hsl(147, 29%, 20%);
  --cor-verde-claro: hsl(79, 53%, 54%);
  --cor-verde-medio: hsl(115, 37%, 52%);
  --cor-verde-escuro: hsl(147, 98%, 23%);
  --cor-alerta: hsla(0, 90%, 44%, 0.75);
  --cor-erro: hsl(14, 54%, 89%);
  --cor-link: hsl(147, 29%, 20%);
  --cor-link-light: hsl(0, 0%, 88%);
  --cor-link-hover: hsl(164, 95%, 26%);
  --cor-menu-fundo: hsl(150, 50%, 99%);
  --cor-menu-hover: hsl(0, 0%, 98%);
  --cor-verde-background: hsl(110, 39%, 91%);
  --cor-inativo: #c8c8c8;
  --cor-divisor: #e1d7bc;
  --cor-ativo: hsl(147deg 25.94% 29.97%);
  --cor-linha: hsl(0, 0%, 79%);
}

/**
 * Outras configurações
 */
:root {
  --fonte-padrao: "Poppins", sans-serif;
  --padding-principal: 1.5em; /* utilizado no cabeçalho e nos containers de conteúdo */
  --border-radius-padrao: 4px;
}
