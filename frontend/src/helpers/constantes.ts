import { TipoNotaFiscal } from "cpesc-shared";

export interface OpcaoNotaFiscal {
  descricao: string;
  codigo: TipoNotaFiscal;
}

export const opcoesNotaFiscal: OpcaoNotaFiscal[] = [
  { descricao: "Importar Nota Fiscal Eletrônica", codigo: TipoNotaFiscal.notaFiscalEletronicaImportacao },
  { descricao: "Digitar Nota Fiscal Eletrônica", codigo: TipoNotaFiscal.notaFiscalEletronicaManual },
  { descricao: "Digitar Nota Fiscal de Serviço", codigo: TipoNotaFiscal.notaServicoManual },
  { descricao: "Digitar Cupom Fiscal", codigo: TipoNotaFiscal.cupomFiscalManual },
  { descricao: "Vincular Reversão", codigo: TipoNotaFiscal.reversao },
];
