import { environment } from "@/env";
import { HttpStatus } from "cpesc-shared";
import type { MetodoHttp, Rota } from "cpesc-shared/out/endpoints/main";
import type { RouteParameters } from "express-serve-static-core";
import type { z, ZodType } from "zod";

export async function chamarApi<TUri extends string, TResposta extends ZodType>(
  rota: Rota<TUri, "get", ZodType, TResposta>,
  queryParams: RouteParameters<TUri>,
  sufixoUrl?: string,
  init?: RequestInit,
): Promise<z.infer<TResposta>>;

export async function chamarApi<
  TUri extends string,
  TMetodo extends Exclude<MetodoHttp, "get">,
  TPayload extends ZodType,
  TResposta extends ZodType,
>(
  rota: Rota<TUri, TMetodo, TPayload, TResposta>,
  queryParams: RouteParameters<TUri>,
  payload: z.infer<TPayload>,
  init?: RequestInit,
): Promise<z.infer<TResposta>>;

export async function chamar<PERSON>pi<
  TUri extends string,
  TMetodo extends MetodoHttp,
  TPayload extends ZodType,
  TResposta extends ZodType,
>(
  rota: Rota<TUri, TMetodo, TPayload, TResposta>,
  queryParams: RouteParameters<TUri>,
  sufixoUrlOuPayload?: string | z.infer<TPayload>,
  init?: RequestInit,
): Promise<z.infer<TResposta>> {
  const sufixoUrl = rota.metodo === "get" ? (sufixoUrlOuPayload as string | undefined) : undefined;
  const payload = rota.metodo === "get" ? undefined : (sufixoUrlOuPayload as z.infer<TPayload>);
  const uri = rota.uri.replace(/:(\w+)/g, (_, chave: string) => queryParams[chave]);
  const response = await fetch(environment.api + uri + (sufixoUrl ?? ""), {
    method: rota.metodo.toUpperCase(),
    body: payload === undefined ? undefined : JSON.stringify(payload),
    headers:
      payload === undefined
        ? undefined
        : {
            "Content-Type": "application/json",
          },
    credentials: "include",
    ...init,
  });
  const status: HttpStatus = response.status;

  if (status === HttpStatus.UNAUTHORIZED) {
    const rotaAtualRelativa = location.href.replace(environment.frontend, "");
    location.href = `/login?redirectTo=${rotaAtualRelativa}`;
    throw new Error("Usuário não autenticado");
  }

  if (status === HttpStatus.FORBIDDEN) {
    location.href = "/acessonegado";
    throw new Error("Usuário não possui permissão para esta funcionalidade");
  }

  if (!response.ok) {
    await response.json().then(
      (json: Record<string, unknown>) => {
        if ("message" in json && typeof json.message === "string") {
          throw new Error(json.message);
        }

        console.error(json);
      },
      async () => console.error(await response.text()),
    );

    const error = new Error(`Erro na rota ${rota.uri}: ${response.status} ${response.statusText}`);
    (error as Error & { status: HttpStatus }).status = response.status;
    throw error;
  }

  return response.json() as z.infer<TResposta>;
}
