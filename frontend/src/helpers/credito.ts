import { type CreditoCartao, SituacaoPrestacaoConta } from "cpesc-shared";

export function getSituacaoCredito(registro: CreditoCartao): SituacaoPrestacaoConta {
  return registro.prestacao.some(p => p.situacao === SituacaoPrestacaoConta.Realizada)
    ? SituacaoPrestacaoConta.Realizada
    : SituacaoPrestacaoConta.Pendente;
}

export function getIdPrestacaoContas(registro: CreditoCartao): number {
  const prestacaoRealizada = registro.prestacao.find(p => p.situacao === SituacaoPrestacaoConta.Realizada);
  return prestacaoRealizada ? prestacaoRealizada.id : 0;
}
