import { environment } from "@/env";
import { useToast } from "@/hooks/useToast";
import telaPadrao from "@/tela-padrao.module.scss";
import { pdf } from "@react-pdf/renderer";
import { Button } from "primereact/button";
import { Calendar } from "primereact/calendar";
import { InputText } from "primereact/inputtext";
import { RadioButton } from "primereact/radiobutton";
import { useState } from "react";
import { type NotaFiscalItemPdf, PdfPorProduto } from "./PdfPorProduto";

export default function RelatorioPorProduto() {
  const [ncm, setNcm] = useState("");
  const [descricao, setDescricao] = useState("");
  const [tipoGasto, setTipoGasto] = useState("todos");
  const [dataInicial, setDataInicial] = useState<Date | null>(null);
  const [dataFinal, setDataFinal] = useState<Date | null>(null);
  const toast = useToast();

  const gerarRelatorio = async () => {
    // Validação de datas
    if (!dataInicial || !dataFinal) {
      toast.show({
        severity: "warn",
        summary: "Aviso!",
        detail: "Por favor, preencha as datas inicial e final.",
        life: 3000,
      });
      return;
    }

    const anoInicial = dataInicial.getFullYear();
    const anoFinal = dataFinal.getFullYear();

    if (anoInicial !== anoFinal) {
      toast.show({
        severity: "warn",
        summary: "Aviso!",
        detail: "A data inicial e a data final devem ser do mesmo ano.",
        life: 3000,
      });
      return;
    }

    if (dataInicial > dataFinal) {
      toast.show({
        severity: "warn",
        summary: "Aviso!",
        detail: "A data inicial não pode ser posterior à data final.",
        life: 3000,
      });
      return;
    }

    // Monta os parâmetros de busca
    const params = new URLSearchParams();
    if (ncm) params.append("ncm", ncm);
    if (descricao) params.append("descricao", descricao);
    if (tipoGasto && tipoGasto !== "todos") params.append("tipo", tipoGasto);
    params.append("dataInicial", dataInicial.toISOString());
    params.append("dataFinal", dataFinal.toISOString());

    const responseGastos = await fetch(`${environment.api}/relatorios/gastos/?${params.toString()}`, {
      method: "GET",
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!responseGastos.ok) {
      toast.show({
        severity: "error",
        summary: "ERRO",
        detail: "Erro ao buscar dados de limite-cartão",
        life: 3000,
      });
      throw new Error("Erro ao buscar dados de limite-cartão");
    }

    const gastos = (await responseGastos.json()) as NotaFiscalItemPdf[]; // TODO: chamar API de forma tipada

    //Caso retornar dados criar PDF
    if (gastos.length > 0) {
      const blob = await pdf(<PdfPorProduto itens={gastos} />).toBlob();
      const url = URL.createObjectURL(blob);
      window.open(url, "_blank");
    } else {
      toast.show({
        severity: "warn",
        summary: "Aviso!",
        detail: "Não foram encontrados dados para o relatório.",
        life: 3000,
      });
    }
  };

  return (
    <main className={telaPadrao.container}>
      <h3>Relatório por Produto</h3>

      <div className="p-fluid grid">
        <div className="col-12 md:col-2">
          <label htmlFor="ncm">NCM</label>
          <InputText id="ncm" value={ncm} onChange={e => setNcm(e.target.value)} />
        </div>

        <div className="col-12 md:col-12">
          <label htmlFor="descricao">Descrição</label>
          <InputText id="descricao" value={descricao} onChange={e => setDescricao(e.target.value)} />
        </div>

        <div className="col-12 md:col-2">
          <label htmlFor="datainicial">Data Inicial</label>
          <Calendar
            id="datainicial"
            value={dataInicial ? new Date(dataInicial) : null}
            dateFormat="dd/mm/yy"
            onChange={e => setDataInicial(e.value ?? null)}
            locale="pt_BR"
            placeholder="__/__/____"
            showButtonBar
            showIcon
            required
          />
        </div>

        <div className="col-12 md:col-2">
          <label>Data Final</label>
          <Calendar
            id="datainicial"
            value={dataFinal ? new Date(dataFinal) : null}
            dateFormat="dd/mm/yy"
            onChange={e => setDataFinal(e.value ?? null)}
            locale="pt_BR"
            placeholder="__/__/____"
            showButtonBar
            showIcon
            required
          />
        </div>

        <div className="col-12">
          <label>Tipo de Gasto</label>
          <div className="flex gap-3 mt-2">
            <RadioButton
              inputId="todos"
              value="todos"
              name="tipo"
              onChange={e => setTipoGasto(e.value as string)}
              checked={tipoGasto === "todos"}
            />
            <label htmlFor="todos">Todos</label>
            <RadioButton
              inputId="material"
              value="M"
              name="tipo"
              onChange={e => setTipoGasto(e.value as string)}
              checked={tipoGasto === "M"}
            />
            <label htmlFor="material">Material</label>
            <RadioButton
              inputId="servico"
              value="S"
              name="tipo"
              onChange={e => setTipoGasto(e.value as string)}
              checked={tipoGasto === "S"}
            />
            <label htmlFor="servico">Serviço</label>
          </div>
        </div>

        <div className="col-12 mt-4">
          <Button
            label="Gerar Relatório"
            icon="pi pi-file-pdf"
            onClick={() => void gerarRelatorio()}
            className={telaPadrao.botaoPrincipal}
          />
        </div>
      </div>
    </main>
  );
}
