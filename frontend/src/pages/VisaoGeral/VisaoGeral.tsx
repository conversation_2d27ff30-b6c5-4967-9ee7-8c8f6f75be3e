import TextoErro from "@/components/TextoErro";
import { useAuth } from "@/contexts/auth/useAuth";
import telaPadrao from "@/tela-padrao.module.scss";

export default function VisaoGeral() {
  const { usuario } = useAuth();
  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
  const autenticado: boolean = usuario !== null;

  return (
    <main>
      <section className={telaPadrao.tituloTela}>
        <h1></h1>
      </section>
      <div className="flex">
        {!autenticado && <TextoErro mensagem="Usuário não autenticado." />}
        {autenticado && <TextoErro mensagem={`Bem-vindo ${usuario.nome}`} />}
      </div>
    </main>
  );
}
