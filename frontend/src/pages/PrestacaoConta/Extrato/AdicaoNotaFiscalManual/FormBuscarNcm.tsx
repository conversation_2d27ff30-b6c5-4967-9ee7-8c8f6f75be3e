import { chamarApi } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";
import type { ItemFiscal } from "cpesc-shared";
import { endpoints } from "cpesc-shared/out/endpoints/main";
import { Column } from "primereact/column";
import { DataTable } from "primereact/datatable";
import estilo from "./AdicaoNotaFiscalManual.module.scss";
import { Dialog } from "primereact/dialog";

export interface FormBuscarNcmProps {
  busca: string;
  tipoGasto: string;
  onCancelarNcm: () => void;
  onSelecaoNcm: (ncm: ItemFiscal) => void;
}

export default function FormBuscarNcm(props: FormBuscarNcmProps) {

  const { data, isLoading, refetch } = useQuery({
    queryKey: ["item-fiscal", props.busca, props.tipoGasto],
    queryFn: () => chamarApi(endpoints.buscarItensFiscaisPorNcmETipoGasto, { ncm: props.busca, tipoGasto: props.tipoGasto }),
    enabled: false,
  });

  if(props.busca && props.busca.length > 3){
    void refetch();
  }else{
    return null;
  }

  const cabecalhoTemplate = () => {
    return (
      <h1>Busca de NCM ou CPS</h1>
    );
  };

  const descricaoTemplate = (options: ItemFiscal) => {
    return (
      <span
        className={estilo.textoWrap}
        title="Clique para selecionar este item"
        onClick={() => props.onSelecaoNcm(options)}
      >
        {options.descricao}
      </span>
    );
  };

  return (
    <Dialog
          visible={true}
          onHide={props.onCancelarNcm}
          closeOnEscape={true}
          modal
          style={{ width: "50vw" }}
          breakpoints={{ "1534px": "60vw", "1280px": "70vw", "1096px": "80vw", "960px": "90vw", "854px": "100vw" }}
          contentStyle={{ background: "hsl(45, 80%, 98%)" }}
    >
      <DataTable
        header={cabecalhoTemplate}
        className={estilo.painelNotaItem}
        loading={isLoading}
        value={data}
        resizableColumns
        showGridlines
        onRowClick={e => {
          props.onSelecaoNcm(e.data as ItemFiscal);
        }}
        responsiveLayout="stack"
        breakpoint="768px"
        rowHover
        emptyMessage="Nenhum resultado encontrado"
      >
        <Column field="ncm" header="NCM" style={{ width: "10%" }} />
        <Column field="descricao" header="Descrição" body={descricaoTemplate} />
      </DataTable>
    </Dialog>
  );
}
