import { chamar<PERSON>pi } from "@/helpers/api";
import { opcoesNotaFiscal } from "@/helpers/constantes";
import type { DeepPartial } from "@/helpers/DeepPartial";
import { formatarMoeda } from "@/helpers/formatacao";
import { useToast } from "@/hooks/useToast";
import telaPadrao from "@/tela-padrao.module.scss";
import { useQuery } from "@tanstack/react-query";
import classNames from "classnames";
import { type NotaFiscal, type NotaFiscalItem, TipoNotaFiscal, validarCnpj } from "cpesc-shared";
import { endpoints } from "cpesc-shared/out/endpoints/main";
import type { PayloadCriarNFManual } from "cpesc-shared/out/endpoints/nota-fiscal";
import { Button } from "primereact/button";
import { Calendar } from "primereact/calendar";
import { Dialog } from "primereact/dialog";
import { InputMask } from "primereact/inputmask";
import { InputNumber } from "primereact/inputnumber";
import { Message } from "primereact/message";
import { type ReactNode, useEffect, useState } from "react";
import {
  type OperacaoTabelaGastos,
  TipoOperacaoTabelaGastos,
} from "../Tabelas/TabelaGastos";
import TabelaAdicaoNFManual from "./TabelaAdicaoNFManual";
import estilo from "./AdicaoNotaFiscalManual.module.scss";

interface AdicaoNotaFiscalManualProps {
  movimentacaoId: number;
  totalMovimentacao: number;
  operacao: OperacaoTabelaGastos;
  onCriar: (nota: PayloadCriarNFManual) => void;
  onAlterar: (nota: NotaFiscal) => void;
  onFechar: () => void;
}

export default function AdicaoNotaFiscalManual(props: AdicaoNotaFiscalManualProps) {
  const [mensagem, setMensagem] = useState<ReactNode>(
    <>
      Clique no botão <i className="pi pi-pencil"></i> para editar os dados. Clicando no botão{" "}
      <i className="pi pi-plus"></i> você pode adicionar mais um item. Você também pode clicar no botão{" "}
      <i className="pi pi-minus"></i> para excluir um item.
    </>,
  );

  const [notaFiscal, setNotaFiscal] = useState<NotaFiscal>({
    id: 0,
    movimentacao_id: props.movimentacaoId,
    tiponotafiscal_id: props.operacao.tipoNota,
    numeroserie: "",
    codigosigef: 0,
    chave: "",
    serie: props.operacao.tipoNota === TipoNotaFiscal.cupomFiscalManual ? null : 0,
    numero: null,
    cnpj: "",
    dataemissao: null,
    valor: null,
    desconto: null,
    cofins: null,
    iss: null,
    pis: null,
    inss: null,
    ir: null,
    notafiscalitens: [
      {
        id: -1,
        ncm: "",
        descricao: "",
        notafiscal_id: 0,
        itemfiscal_id: null,
        quantidade: 0,
        unidade: "",
        valorunitario: null,
        valor: 0,
        itemfiscal: null,
      },
    ],
  });

  const [itensEmEdicao, setItensEmEdicao] = useState(new Map<number, Partial<NotaFiscalItem>>([[-1, {}]]));
  const toast = useToast();

  // Busca a nota fiscal no banco de dados
  const { data, isLoading } = useQuery<NotaFiscal | undefined>({
    queryKey: ["/nota-fiscal", props.movimentacaoId],
    queryFn: async () => {
      const resultados = await chamarApi(
        endpoints.buscarNotasFiscais,
        {},
        `?movimentacao_id=${props.movimentacaoId.toString()}`,
      );

      return resultados[0];
    },
    enabled: props.operacao.tipoOperacao === TipoOperacaoTabelaGastos.EditarManual,
  });

  useEffect(() => {
    if (data) {
      setNotaFiscal({
        ...data,
      });

      setItensEmEdicao(new Map());
    }
  }, [data]);

  const liberarBotaoSalvar =
    (props.operacao.tipoNota === TipoNotaFiscal.cupomFiscalManual || notaFiscal.serie !== null) &&
    notaFiscal.numero !== null &&
    notaFiscal.dataemissao !== null &&
    Object.keys(itensEmEdicao).length === 0 &&
    validarCnpj(notaFiscal.cnpj ?? "");

  // Salva a nota fiscal no banco de dados e passa resultado para o componente pai
  function botaoSalvarNota() {
    // Verifica se todos os campos obrigatórios dos itens estão preenchidos
    const itens = notaFiscal.notafiscalitens ?? [];
    if (
      !validarCnpj(notaFiscal.cnpj ?? "") ||
      (itens.every(item => item.ncm != "0") && itens.every(item => item.ncm.length < 4)) ||
      !itens.every(item => item.valorunitario != null) ||
      !itens.every(item => (item.valorunitario ?? 0) > 0)
    ) {
      toast.show({
        severity: "error",
        summary: "Erro",
        detail: "Todos os itens devem ser preenchidos com valores válidos!",
        life: 3000, // Tempo de exibição em milissegundos
      });
      return;
    }

    // Verifica se todos os campos obrigatórios estão preenchidos
    if (
      notaFiscal.numero == null ||
      (props.operacao.tipoNota !== TipoNotaFiscal.cupomFiscalManual && notaFiscal.serie === null) ||
      notaFiscal.cnpj == null ||
      notaFiscal.dataemissao == null ||
      notaFiscal.notafiscalitens == null
    ) {
      toast.show({
        severity: "error",
        summary: "Erro",
        detail: "Campos obrigatórios não preenchidos, insira valores válidos!",
      });
      return;
    }
    // Nota fiscal completa
    else {
      // Verifica se a soma dos itens confere com o valor da movimentação
      if (somaSubtotal !== props.totalMovimentacao) {
        toast.show({
          severity: "warn",
          summary: "ATENÇÃO",
          detail:
            "A soma dos itens lançados não confere com o valor do gasto. Lembre-se de corrigir antes de Finalizar a Prestação de Contas.",
          life: 5000,
        });
      }
      const nota: DeepPartial<NotaFiscal> = {
        tiponotafiscal_id: props.operacao.tipoNota,
        movimentacao_id: props.movimentacaoId,
        notafiscalitens: notaFiscal.notafiscalitens,
        codigosigef: null,
        serie: notaFiscal.serie,
        numero: notaFiscal.numero,
        cnpj: notaFiscal.cnpj,
        dataemissao: notaFiscal.dataemissao,
        valor: somaSubtotal,
        desconto: notaFiscal.desconto ?? null,
        cofins: notaFiscal.cofins ?? null,
        iss: notaFiscal.iss ?? null,
        pis: notaFiscal.pis ?? null,
        inss: notaFiscal.inss ?? null,
        ir: notaFiscal.ir ?? null,
      };

      for (const item of nota.notafiscalitens ?? []) {
        if (item.id !== undefined && item.id <= 0) {
          delete item.id;
        }

        item.notafiscal_id = notaFiscal.id;
      }

      if (notaFiscal.id > 0) {
        nota.id = notaFiscal.id;
        props.onAlterar(nota as NotaFiscal);
      } else {
        props.onCriar(nota as PayloadCriarNFManual);
      }
    }
  }

  // Adiciona nova linha na tabela de itens da nota fiscal
  const adicionarLinha = () => {
    const id = -((notaFiscal.notafiscalitens?.length ?? 0) + 1);

    const novaLinha: NotaFiscalItem = {
      id,
      ncm: "",
      descricao: "",
      notafiscal_id: 0,
      itemfiscal_id: null,
      quantidade: 0,
      unidade: "",
      valorunitario: 0,
      valor: 0,
      itemfiscal: null,
    };

    setNotaFiscal({
      ...notaFiscal,
      notafiscalitens: [...(notaFiscal.notafiscalitens ?? []), novaLinha],
    });

    const itensNovos = itensEmEdicao.set(id, {});

    setItensEmEdicao(itensNovos);

    setMensagem(
      <>
        Clique no botão <i className="pi pi-pencil"></i> para editar os dados. Após a Edição clique no botão{" "}
        <i className="pi pi-check"></i> para confirmar ou no botão <i className="pi pi-times"></i> para cancelar.
      </>,
    );
  };

  const impostos =
    (notaFiscal.cofins ?? 0) +
    (notaFiscal.iss ?? 0) +
    (notaFiscal.pis ?? 0) +
    (notaFiscal.inss ?? 0) +
    (notaFiscal.ir ?? 0);
  const somaSubtotal = notaFiscal.notafiscalitens
    ? notaFiscal.notafiscalitens.reduce((sum, item) => {
        return sum + item.valor;
      }, 0) +
      impostos -
      (notaFiscal.desconto ?? 0)
    : 0;

  const ptdialog = {
    headerIcons: {
      className: estilo.iconesCabecalho,
    },
  };

  return (
    <Dialog
      pt={ptdialog}
      visible={true}
      onHide={props.onFechar}
      closeOnEscape={true}
      modal
      maximizable
      breakpoints={{ "1534px": "60vw", "1280px": "70vw", "1096px": "80vw", "960px": "90vw", "854px": "100vw" }}
      contentStyle={{ background: "hsl(45, 80%, 98%)" }}
      header={opcoesNotaFiscal.filter(opcao => opcao.codigo === props.operacao.tipoNota).map(opcao => opcao.descricao)}
      headerStyle={{ background: "hsl(45, 80%, 98%)" }}
    >
      <div className={estilo.conteudoDialog}>
        <div className={classNames(estilo.conteudoCarregadoDialog, isLoading && estilo.carregando)}>
          <div className={estilo.painelNota}>
            <label htmlFor="cnpj">CNPJ do Estabelecimento: </label>
            <InputMask
              id="cnpj"
              mask="**.***.***/****-99"
              keyfilter={/[A-Z0-9]/}
              title="Apenas números e letras maiúsculas são permitidos."
              className={estilo.caixaInputMask}
              autoFocus
              placeholder="00.ABY.Z00/0AZ0-00"
              value={notaFiscal.cnpj?.toLocaleUpperCase() ?? ""}
              onChange={e => {
                const cnpj = e.target.value?.replace(/[^A-Za-z0-9]/g, "") ?? "";
                if (cnpj.length < 14) {
                  setNotaFiscal({ ...notaFiscal, cnpj: e.target.value?.toLocaleUpperCase() ?? "" });
                } else {
                  if (validarCnpj(cnpj)) {
                    setNotaFiscal({ ...notaFiscal, cnpj });
                  } else {
                    toast.show({
                      severity: "error",
                      summary: "Erro",
                      detail: "CNPJ inválido!",
                      life: 3000,
                    });
                  }
                }
              }}
              required
            />
            <label htmlFor="numero">
              {props.operacao.tipoNota === TipoNotaFiscal.cupomFiscalManual
                ? "Cupom Fiscal: "
                : "Número da Nota Fiscal: "}{" "}
            </label>
            <InputNumber
              id="number"
              maxLength={10}
              useGrouping={false}
              className={estilo.caixaInput}
              value={notaFiscal.numero ?? null}
              onChange={e => {
                setNotaFiscal({ ...notaFiscal, numero: e.value });
              }}
              required
            />
            {props.operacao.tipoNota !== TipoNotaFiscal.cupomFiscalManual && (
              <>
                <label htmlFor="serie">Série da Nota: </label>
                <InputNumber
                  maxLength={10}
                  useGrouping={false}
                  className={estilo.caixaInputMenor}
                  value={notaFiscal.serie ?? null}
                  onChange={e => {
                    setNotaFiscal({ ...notaFiscal, serie: e.value });
                  }}
                  required
                />
              </>
            )}
            <label htmlFor="dataemissao">Data emissão: </label>
            <Calendar
              id="dataemissao"
              className={estilo.caixaInput}
              value={notaFiscal.dataemissao ? new Date(notaFiscal.dataemissao) : null}
              dateFormat="dd/mm/yy"
              onChange={e => {
                setNotaFiscal({ ...notaFiscal, dataemissao: e.value?.toISOString() ?? null });
              }}
              locale="pt_BR"
              placeholder="__/__/____"
              showButtonBar
              showIcon
              required
            />
          </div>
          <div className={estilo.painelBotao}>
            <label htmlFor="valor">Valor do Gasto: </label>
            <span id="valor" className={estilo.caixaInputNegrito}>
              {formatarMoeda(props.totalMovimentacao)}
            </span>
          </div>
          <div className={estilo.painelNotaItem}>
            {notaFiscal.tiponotafiscal_id === TipoNotaFiscal.notaServicoManual && (
              <>
                <span className={estilo.mensagem}>Retenções:</span>
                <div className={estilo.painelNota}>
                  <label htmlFor="cofins">COFINS:</label>
                  <InputNumber
                    id="cofins"
                    locale="pt-BR"
                    mode="currency"
                    currency="BRL"
                    value={notaFiscal.cofins ?? 0}
                    onChange={e => {
                      setNotaFiscal({ ...notaFiscal, cofins: e.value });
                    }}
                    className={estilo.caixaInput}
                  />
                  <label htmlFor="iss">ISS:</label>
                  <InputNumber
                    id="iss"
                    locale="pt-BR"
                    mode="currency"
                    currency="BRL"
                    value={notaFiscal.iss ?? 0}
                    onChange={e => {
                      setNotaFiscal({ ...notaFiscal, iss: e.value });
                    }}
                    className={estilo.caixaInput}
                  />
                  <label htmlFor="pis">PIS:</label>
                  <InputNumber
                    id="pis"
                    locale="pt-BR"
                    mode="currency"
                    currency="BRL"
                    value={notaFiscal.pis ?? 0}
                    onChange={e => {
                      setNotaFiscal({ ...notaFiscal, pis: e.value });
                    }}
                    className={estilo.caixaInput}
                  />
                  <label htmlFor="inss">INSS:</label>
                  <InputNumber
                    id="inss"
                    locale="pt-BR"
                    mode="currency"
                    currency="BRL"
                    value={notaFiscal.inss ?? 0}
                    onChange={e => {
                      setNotaFiscal({ ...notaFiscal, inss: e.value });
                    }}
                    className={estilo.caixaInput}
                  />
                  <label htmlFor="ir">IR:</label>
                  <InputNumber
                    id="ir"
                    locale="pt-BR"
                    mode="currency"
                    currency="BRL"
                    value={notaFiscal.ir ?? 0}
                    onChange={e => {
                      setNotaFiscal({ ...notaFiscal, ir: e.value });
                    }}
                    className={estilo.caixaInput}
                  />
                </div>
              </>
            )}
            <div className={estilo.painelBotaoAdicionar}>
              <Button
                icon="pi pi-plus"
                className={classNames(telaPadrao.botaoPrincipal, "mb-1")}
                onClick={adicionarLinha}
              />
            </div>

            <TabelaAdicaoNFManual
              notaFiscal={notaFiscal}
              setNotaFiscal={setNotaFiscal}
              itensEmEdicao={itensEmEdicao}
              setItensEmEdicao={setItensEmEdicao}
            />

            <div className={estilo.painelBotao}>
              <label htmlFor="desconto">Desconto:</label>
              <InputNumber
                id="desconto"
                locale="pt-BR"
                mode="currency"
                currency="BRL"
                value={notaFiscal.desconto ?? 0}
                onChange={e => {
                  setNotaFiscal({ ...notaFiscal, desconto: e.value });
                }}
                className={estilo.caixaInputNegrito}
              />
            </div>
            <div className={estilo.painelBotao}>
              <label>Soma da Nota: {formatarMoeda(somaSubtotal)}</label>
            </div>
          </div>

          <div className={estilo.painelBotao}>
            <Button
              className={telaPadrao.botaoPrincipal}
              label="Salvar"
              disabled={!liberarBotaoSalvar}
              onClick={botaoSalvarNota}
            />
          </div>

          {mensagem && <Message severity="info" text={mensagem} className={estilo.mensagemInferior} />}
        </div>
      </div>
    </Dialog>
  );
}
