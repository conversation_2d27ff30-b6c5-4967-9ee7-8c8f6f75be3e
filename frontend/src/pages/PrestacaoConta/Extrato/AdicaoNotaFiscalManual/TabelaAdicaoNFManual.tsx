import { formatarMoeda } from "@/helpers/formatacao";
import { useToast } from "@/hooks/useToast";
import { Button } from "primereact/button";
import { InputNumber } from "primereact/inputnumber";
import { InputText } from "primereact/inputtext";
import { Fragment } from "react";
import estilo from "./AdicaoNotaFiscalManual.module.scss";
import { NotaFiscal, NotaFiscalItem } from "cpesc-shared";
import DropdownBuscarNcm from "./DropDownBuscarNcm";

export interface TabelaAdicaoNFManualProps {
  notaFiscal: NotaFiscal;
  setNotaFiscal: (notaFiscal: NotaFiscal) => void;
  itensEmEdicao: Map<number, Partial<NotaFiscalItem>>;
  setItensEmEdicao: (itensEmEdicao: Map<number, Partial<NotaFiscalItem>>) => void;
}

export default function TabelaAdicaoNFManual(props: TabelaAdicaoNFManualProps) {
  const toast = useToast();

  const valorUnitarioTemplate = (rowData: NotaFiscalItem) => {
    if (rowData.valorunitario) {
      return formatarMoeda(rowData.valorunitario);
    }
    return null;
  };

  const editorNcm = (rowData: NotaFiscalItem) => {
    const valor = props.itensEmEdicao.get(rowData.id)?.ncm ?? "";
    const isValido = valor === "0" || valor.length >= 4;
    return (
      <InputNumber
        name="ncm"
        className={`${estilo.caixaInputMenor} ${!isValido ? estilo.inputInvalid : ""}`}
        value={isNaN(parseInt(valor)) ? undefined : parseInt(valor)}
        onChange={e => {
          const novosItensEmEdicao = new Map(props.itensEmEdicao);
          novosItensEmEdicao.set(rowData.id, {
            ...novosItensEmEdicao.get(rowData.id),
            ncm: e.value?.toString() ?? "",
          });
          props.setItensEmEdicao(novosItensEmEdicao);
        }}
        onBlur={e => {
          const novosItensEmEdicao = new Map(props.itensEmEdicao);
          novosItensEmEdicao.set(rowData.id, {
            ...novosItensEmEdicao.get(rowData.id),
            ncm: e.target.value.toString(),
          });
          props.setItensEmEdicao(novosItensEmEdicao);
        }}
        required
        useGrouping={false}
        maxLength={8}
        title="Informe um NCM válido com pelo menos 4 dígitos. Caso não saiba, informe 0"
        locale="pt-BR"
      />
    );
  };

  const editorNcmDescricao = (rowData: NotaFiscalItem) => {
    return (
      <InputText
        type="text"
        className={estilo.caixaInputGrande}
        value={props.itensEmEdicao.get(rowData.id)?.descricao ?? ""}
        onChange={e => {
          const novosItensEmEdicao = new Map(props.itensEmEdicao);
          novosItensEmEdicao.set(rowData.id, {
            ...novosItensEmEdicao.get(rowData.id),
            descricao: e.target.value,
          });
          props.setItensEmEdicao(novosItensEmEdicao);
        }}
        required
      />
    );
  };

  const editorBuscaNcm = (rowData: NotaFiscalItem) => {
    console.log("editorBuscaNcm: ",rowData)
    return (
      <DropdownBuscarNcm />
    );
  };

  const editorQuantidade = (item: NotaFiscalItem) => {
    const valor = props.itensEmEdicao.get(item.id)?.quantidade ?? null;
    const isValido = valor != null && !isNaN(valor) && valor >= 1;

    return (
      <InputNumber
        name="quantidade"
        value={valor}
        onChange={e => {
          const novosItensEmEdicao = new Map(props.itensEmEdicao);
          novosItensEmEdicao.set(item.id, {
            ...novosItensEmEdicao.get(item.id),
            quantidade: e.value ?? 0,
          });
          props.setItensEmEdicao(novosItensEmEdicao);
        }}
        onBlur={e => {
          const novosItensEmEdicao = new Map(props.itensEmEdicao);
          const valorParsed = parseInt(e.target.value);
          novosItensEmEdicao.set(item.id, {
            ...novosItensEmEdicao.get(item.id),
            quantidade: isNaN(valorParsed) ? undefined : valorParsed,
          });
          props.setItensEmEdicao(novosItensEmEdicao);
        }}
        className={`${estilo.caixaInputMenor} ${!isValido ? estilo.inputInvalid : ""}`}
        useGrouping={false}
        min={1}
        locale="pt-BR"
        required
      />
    );
  };

  const editorUnidade = (item: NotaFiscalItem) => {
    return (
      <InputText
        type="text"
        className={estilo.caixaInputMenor}
        value={props.itensEmEdicao.get(item.id)?.unidade ?? ""}
        onChange={e => {
          const novosItensEmEdicao = new Map(props.itensEmEdicao);
          novosItensEmEdicao.set(item.id, {
            ...novosItensEmEdicao.get(item.id),
            unidade: e.target.value,
          });
          props.setItensEmEdicao(novosItensEmEdicao);
        }}
        required
      />
    );
  };

  const editorValorUnitario = (item: NotaFiscalItem) => {
    return (
      <InputNumber
        name="valorunitario"
        value={props.itensEmEdicao.get(item.id)?.valorunitario ?? null}
        onChange={e => {
          const novosItensEmEdicao = new Map(props.itensEmEdicao);
          novosItensEmEdicao.set(item.id, {
            ...novosItensEmEdicao.get(item.id),
            valorunitario: e.value,
          });
          props.setItensEmEdicao(novosItensEmEdicao);
        }}
        onBlur={e => {
          const novosItensEmEdicao = new Map(props.itensEmEdicao);
          // Remove tudo exceto dígitos e vírgula decimal
          const valorLimpo = e.target.value
            .replace(/[^\d,]/g, "") // mantém apenas números e vírgula
            .replace(/,+/g, ",") // evita múltiplas vírgulas
            .replace(/^,/, ""); // remove vírgula no início

          // Troca vírgula por ponto para parseFloat
          const valorNumerico = parseFloat(valorLimpo.replace(",", "."));

          novosItensEmEdicao.set(item.id, {
            ...novosItensEmEdicao.get(item.id),
            valorunitario: isNaN(valorNumerico) ? null : valorNumerico,
          });
          props.setItensEmEdicao(novosItensEmEdicao);
        }}
        mode="currency"
        currency="BRL"
        locale="pt-BR"
        className={estilo.caixaInput}
        required
      />
    );
  };
  
  return (
      <table className={estilo.tabelaNotaItem}>
        <thead>
          <tr>
            <th scope="row" style={{ width: "5%" }}>
              NCM
            </th>
            <th scope="row" style={{ width: "15%" }}>
              Busca NCM
            </th>
            <th scope="row" style={{ width: "15%" }}>
              Descrição
            </th>
            <th scope="row" style={{ width: "10%" }}>
              Quantidade
            </th>
            <th scope="row" style={{ width: "10%" }}>
              Unidade
            </th>
            <th scope="row" style={{ width: "10%" }}>
              Valor Unitário
            </th>
            <th scope="row" style={{ width: "10%" }}>
              Subtotal (AUTO)
            </th>
            <th scope="row" style={{ width: "3%" }} />
            <th scope="row" style={{ width: "3%" }} />
          </tr>
        </thead>
        <tbody>
          {props.notaFiscal.notafiscalitens?.map(item => {
            const editandoItem = props.itensEmEdicao.has(item.id);
            const itemPendente = props.itensEmEdicao.get(item.id);

            return (
              <Fragment key={item.id}>
                <tr>
                  <td>{editandoItem ? editorNcm(item) : item.ncm}</td>
                  <td>{editandoItem ? editorBuscaNcm(item) : item.itemfiscal?.descricao}</td>
                  <td>{editandoItem ? editorNcmDescricao(item) : item.descricao}</td>
                  <td>{editandoItem ? editorQuantidade(item) : item.quantidade}</td>
                  <td>{editandoItem ? editorUnidade(item) : item.unidade}</td>
                  <td>{editandoItem ? editorValorUnitario(item) : valorUnitarioTemplate(item)}</td>
                  <td>
                    {formatarMoeda(
                      editandoItem
                        ? (itemPendente?.quantidade ?? 0) * (itemPendente?.valorunitario ?? 0)
                        : item.quantidade * (item.valorunitario ?? 0),
                    )}
                  </td>
                  <td className="text-center">
                    {editandoItem ? (
                      <div className="flex gap-1 justify-content-center">
                        <Button
                          onClick={() => {
                            const testaNuloOuVazio = (valor: string | number | null | undefined) =>
                              valor == null || valor.toString().trim() === "";

                            if (
                              testaNuloOuVazio(itemPendente?.ncm) ||
                              (itemPendente?.ncm !== undefined &&
                                itemPendente.ncm != "0" &&
                                itemPendente.ncm.length < 4) ||
                              testaNuloOuVazio(itemPendente?.descricao) ||
                              testaNuloOuVazio(itemPendente?.quantidade) ||
                              testaNuloOuVazio(itemPendente?.unidade) ||
                              testaNuloOuVazio(itemPendente?.valorunitario)
                            ) {
                              toast.show({
                                severity: "error",
                                summary: "Erro",
                                detail:
                                  "Todos os itens da tabela devem ser preenchidos com valores válidos! Edite os dados digitados.",
                                life: 3000, // Tempo de exibição em milissegundos
                              });

                              return;
                            }

                            const novaNotaFiscal = {
                              ...props.notaFiscal,
                              notafiscalitens:
                                props.notaFiscal.notafiscalitens?.map(notaItem => {
                                  if (notaItem.id === item.id) {
                                    return {
                                      ...notaItem,
                                      ...itemPendente,
                                      valor: (itemPendente?.quantidade ?? 0) * (itemPendente?.valorunitario ?? 0),
                                    };
                                  }

                                  return notaItem;
                                }) ?? null,
                            };
                            props.setNotaFiscal(novaNotaFiscal);
                            const novosItensEmEdicao = new Map(props.itensEmEdicao);
                            novosItensEmEdicao.delete(item.id);
                            props.setItensEmEdicao(novosItensEmEdicao);
                          }}
                          className={estilo.botaoEditar}
                        >
                          <i className="pi pi-check" />
                        </Button>

                        <Button
                          onClick={() => {
                            const novosItensEmEdicao = new Map(props.itensEmEdicao);
                            novosItensEmEdicao.delete(item.id);
                            props.setItensEmEdicao(novosItensEmEdicao);
                          }}
                          className={estilo.botaoEditar}
                        >
                          <i className="pi pi-times" />
                        </Button>
                      </div>
                    ) : (
                      <Button
                        onClick={() => props.setItensEmEdicao(new Map(props.itensEmEdicao).set(item.id, { ...item }))}
                        className={estilo.botaoEditar}
                      >
                        <i className="pi pi-pencil" />
                      </Button>
                    )}
                  </td>
                  <td className="text-center">
                    <Button
                      onClick={() => {
                        const novaNotaFiscal = {
                          ...props.notaFiscal,
                          notafiscalitens:
                            props.notaFiscal.notafiscalitens?.filter(notaItem => notaItem.id !== item.id) ?? null,
                        };
                        props.setNotaFiscal(novaNotaFiscal);
                        const novosItensEmEdicao = new Map(props.itensEmEdicao);
                        novosItensEmEdicao.delete(item.id);
                        props.setItensEmEdicao(novosItensEmEdicao);

                      }}
                    >
                      <i className="pi pi-trash" />
                    </Button>
                  </td>
                </tr>
              </Fragment>
            );
          })}
        </tbody>
      </table>
  );
}
