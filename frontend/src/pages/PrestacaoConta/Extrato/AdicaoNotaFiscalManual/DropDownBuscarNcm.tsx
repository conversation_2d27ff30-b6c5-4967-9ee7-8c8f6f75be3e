import { Dropdown } from "primereact/dropdown";
import { useRef, useState } from "react";
import estilo from "./AdicaoNotaFiscalManual.module.scss";


export default function DropdownBuscarNcm() {
    const [selectedItem, setSelectedItem] = useState<string>("");
    const items = useRef(Array.from({ length: 100000 }));
    const [loading, setLoading] = useState(false);
    const loadLazyTimeout = useRef<NodeJS.Timeout | null>(null);

    const onLazyLoad = (event: { first: number; last: number }) => {
        setLoading(true);

        if (loadLazyTimeout.current) {
            clearTimeout(loadLazyTimeout.current);
        }

        //imitate delay of a backend call
        loadLazyTimeout.current = setTimeout(
            () => {
                const { first, last } = event;
                const _items = [...items.current];

                for (let i = first; i < last; i++) {
                    _items[i] = { label: `Item #${i}`, value: i.toString() };
                }

                items.current = _items;
                setLoading(false);
            },
            Math.random() * 1000 + 250
        );
    };

    return (
          <div className="card flex justify-content-center">
              <Dropdown
                  value={selectedItem}
                  onChange={(e) => setSelectedItem(e.value as number | string)}
                  options={items.current}
                  placeholder="Digite uma descrição..."
                  emptyMessage="Necessário informar pelo menos 4 caracteres..."
                  className={typeof selectedItem === "string" && selectedItem.length > 3 ? estilo.caixaInputGrande: estilo.inputInvalid}
                  virtualScrollerOptions={{ lazy: true, onLazyLoad, itemSize: 38, showLoader: true, loading, delay: 250 }}
                  required
                  editable
              />
          </div>
      )
}