@use "@/variables.scss";

@keyframes piscarSombra {
  0% {
    box-shadow: 0 0 1px 1px rgba(255, 0, 0, 0.8);
  }
  40% {
    box-shadow: 0 0 4px 4px rgba(255, 0, 0, 0.3);
  }
  80% {
    box-shadow: 0 0 1px 1px rgba(255, 0, 0, 0.8);
  }
  100% {
    box-shadow: none;
  }
}

@keyframes piscarSombra {
  0% {
    box-shadow: 0 0 1px 1px rgba(255, 0, 0, 0.8);
  }
  40% {
    box-shadow: 0 0 4px 4px rgba(255, 0, 0, 0.3);
  }
  80% {
    box-shadow: 0 0 1px 1px rgba(255, 0, 0, 0.8);
  }
  100% {
    box-shadow: none;
  }
}

input:required:invalid {
  animation: piscarSombra 4s infinite;
}

.conteudoDialog {
  position: relative;

  .spinner {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  .conteudoCarregadoDialog.carregando {
    opacity: 0.2;
  }
}

.painelNota {
  font-size: 1rem;
  font-weight: bold;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: baseline;
  padding: 1rem;
  width: 100%;
  border: 1px solid var(--cor-verde-background);
  border-radius: var(--border-radius-padrao);

  @media only screen and (max-width: 640px) {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    align-content: space-around;
  }
  & input:required:invalid {
    animation: piscarSombra 4s infinite;
  }
}

.iconesCabecalho {
  gap: 1rem;
}

.colunaCampos {
  display: flex;
  column-gap: 0.5rem;
  row-gap: 0.1rem;
}

.colunaTitulo {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: space-around;
  align-items: flex-end;
}

.colunaInput {
  display: flex;
  flex-direction: column;
  row-gap: 0.5rem;
  justify-content: space-around;
  @media only screen and (max-width: 768px) {
    row-gap: unset;
  }
}

.colunaTituloTotal {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-items: flex-start;
  margin-top: 10px;
}

.caixaInput {
  background-color: white;
  font-size: 0.8rem;
  border-radius: var(--border-radius-padrao);
  margin: 0;

  input {
    font-size: 0.8rem;
    border-radius: var(--border-radius-padrao);
    margin: 0;
    width: 6rem;
  }
}

.caixaInputGrande {
  font-size: 0.8rem;
  border-radius: var(--border-radius-padrao);
  margin: 0;
  width: 20rem;
}

.caixaInputMenor {
  font-size: 0.8rem;
  border-radius: var(--border-radius-padrao);
  margin: 0;
  width: 4rem;
  & input {
    font-size: 0.8rem;
    border-radius: var(--border-radius-padrao);
    margin: 0;
    width: 4rem;
  }
}

.caixaInputNegrito {
  margin: 10px 0;
  width: 7rem;
  text-align: right;
  font-weight: bold;
  & input {
    margin: 10px 0;
    width: 7rem;
    text-align: right;
    font-weight: bold;
  }
}

.caixaInputMask {
  font-size: 0.8rem;
  border-radius: var(--border-radius-padrao);
  margin: 0;
}

.painelBotao {
  display: flex;
  gap: 10px;
  justify-content: right;
  align-items: center;
  margin: 10px 0;
}

.painelBotaoAdicionar {
  display: flex;
  justify-content: right;
  padding: 0;
  & button {
    margin: 0;
  }
  & label {
    font-size: 0.8rem;
  }
}

.painelNotaItem {
  display: flex;
  flex-direction: column;
  width: 100%;
  :global {
    .p-datatable .p-datatable-header {
      padding: 0;
      background-color: var(--cor-fundo-padrao);
    }
  }
}

.textoWrap {
  font-size: 0.8rem;
  border-radius: var(--border-radius-padrao);
  margin: 0;
  text-wrap: wrap;
}

.ncmSearchExpanded {
  padding: 1rem;
  background-color: var(--surface-100);
  border-radius: 6px;
  margin: 0.5rem 0;
}

.ncmSearchHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.searchContainer {
  display: flex;
  gap: 0.5rem;
  flex: 1;
  margin-right: 1rem;
}

.buttonContainer {
  display: flex;
  gap: 0.5rem;
}

.tabelaNotaItem {
  border-collapse: collapse;
  border-spacing: 0;
  font-size: 0.75rem;
  margin-bottom: 1rem;

  thead {
    background: var(--cor-verde-background);
    text-align: left;
  }

  th,
  td {
    border: 1px solid #e9ecef;
    padding: 0.75rem;
  }
}

.inputInvalid {
  animation: piscarSombra 4s infinite;
}
