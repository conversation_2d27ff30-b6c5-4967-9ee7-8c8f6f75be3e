import { chamarApi } from "@/helpers/api";
import { formatarDataYYYYMMDD } from "@/helpers/formatacao";
import { pdf } from "@react-pdf/renderer";
import type { Credito, Movimentacao, UsuarioAutenticado } from "cpesc-shared";
import { endpoints } from "cpesc-shared/out/endpoints/main";
import { PdfPrestacaoContas } from "./Relatorios/PdfPrestacaoContas";

export interface DadosFinalizarCredito {
  credito: Credito;
  movimentacoes: Movimentacao[];
  totalGastos: number;
  orgao: string;
  processo: string;
  ano: string;
  usuario: UsuarioAutenticado;
}

async function salvarPrestacaoContas(dados: DadosFinalizarCredito) {
  await chamarApi(
    endpoints.criarPrestacaoContas,
    {},
    {
      limiteCartaoId: dados.credito.id,
      valorGasto: dados.totalGastos,
      orgaoSgpe: dados.orgao,
      processoSgpe: dados.processo,
      anoSgpe: dados.ano,
    },
  );
}

async function finalizarLimiteCartao(limiteCartaoId: number) {
  await chamarApi(endpoints.finalizarLimiteCartao, { idCredito: limiteCartaoId.toString() }, undefined);
}

async function atualizarMovimentacoes(movimentacaoIds: number[], limiteCartaoId: number) {
  await chamarApi(
    endpoints.associarLimiteMovimentacao,
    {},
    {
      movimentacaoIds,
      limiteCartaoId,
    },
  );
}

export async function finalizarCredito(dados: DadosFinalizarCredito) {
  await salvarPrestacaoContas(dados);
  await finalizarLimiteCartao(dados.credito.id);

  if (dados.movimentacoes.length > 0) {
    await atualizarMovimentacoes(
      dados.movimentacoes.map(m => m.id),
      dados.credito.id,
    );
  }
  await imprimirExtrato(dados);
  return true;
}

export async function imprimirExtrato(dados: DadosFinalizarCredito) {
  const blob = await pdf(await PdfPrestacaoContas(dados)).toBlob();
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download = `DemonstrativoGastos_${formatarDataYYYYMMDD(new Date()) + "_" + dados.credito.id}.pdf`;
  link.click();
}
