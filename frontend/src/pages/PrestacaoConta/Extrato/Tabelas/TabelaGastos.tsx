import SpinnerCpesc from "@/components/SpinnerCpesc";
import Tabela from "@/components/Tabela/Tabela";
import { useAuth } from "@/contexts/auth/useAuth";
import AdicaoNotaFiscalManual from "@/pages/PrestacaoConta/Extrato/AdicaoNotaFiscalManual/AdicaoNotaFiscalManual";
import { chamarApi } from "@/helpers/api";
import { opcoesNotaFiscal } from "@/helpers/constantes";
import { aplicarMascaraCNPJ, formatarData, formatarDataHora, formatarMoeda } from "@/helpers/formatacao";
import { useExtratoPrestacaoContaStore } from "@/stores/ExtratoPrestacaoContaStore";
import telaPadrao from "@/tela-padrao.module.scss";
import { useMutation } from "@tanstack/react-query";
import classNames from "classnames";
import {
  type Movimentacao,
  type NotaFiscal,
  type NotaFiscalItem,
  type ReversaoMovimentacao,
  SituacaoPrestacaoConta,
  TipoNotaFiscal,
} from "cpesc-shared";
import { endpoints } from "cpesc-shared/out/endpoints/main";
import type { PayloadCriarNF } from "cpesc-shared/out/endpoints/nota-fiscal";
import type { PayloadCriarReversaoMovimentacao } from "cpesc-shared/out/endpoints/reversao-movimentacao";
import { Button } from "primereact/button";
import { Checkbox } from "primereact/checkbox";
import { Column, type ColumnProps } from "primereact/column";
import { confirmDialog } from "primereact/confirmdialog";
import { DataTable } from "primereact/datatable";
import { Dropdown, type DropdownChangeEvent } from "primereact/dropdown";
import { Tag } from "primereact/tag";
import { useState } from "react";
import AdicaoNotaFiscalImportacao from "../Adicao-Nota-Fiscal-Importacao/Adicao-Nota-Fiscal-Importacao";
import estilo from "../ExtratoPrestacaoConta.module.scss";
import VincularReversao from "../Vincular-Reversao/Vincular-Reversao";

export interface NotaByData {
  data: string;
  valor: number;
  cnpj: string;
}

interface ColunasMeta extends ColumnProps {
  key: string;
}

interface TabelaGastosProps {
  gastos: Movimentacao[] | undefined;
  situacaoPc: SituacaoPrestacaoConta;
  onVincularNotaImportacao: (nota: PayloadCriarNF) => void;
  onCriarNotaManual: (nota: PayloadCriarNF) => void;
  onAlterarNota: (nota: NotaFiscal) => void;
  onExcluirNota: () => void;
  onVincularReversao: (reversao: Omit<PayloadCriarReversaoMovimentacao, "limitecartao_id">) => void;
  onExcluirReversao: () => void;
}

interface DadosLinhaTabela extends Movimentacao {
  selecionado: boolean;
  atualizando: boolean;
}

export interface OperacaoTabelaGastos {
  tipoOperacao: TipoOperacaoTabelaGastos;
  tipoNota: TipoNotaFiscal;
}

export enum TipoOperacaoTabelaGastos {
  AdicionarManual,
  EditarManual,
  AdicionarImportacao,
  EditarImportacao,
}

export function TabelaGastos(props: TabelaGastosProps) {
  const { usuario } = useAuth();
  const [movimentacaoSelecionada, setMovimentacaoSelecionada] = useState<Movimentacao | null>(null);
  const [movimentacoesReversao, setMovimentacoesReversao] = useState<Movimentacao[] | null>(null);
  const [operacao, setOperacao] = useState<OperacaoTabelaGastos | null>(null);
  const gastosSelecionados = useExtratoPrestacaoContaStore(state => state.gastosSelecionados);
  const setSelecaoGasto = useExtratoPrestacaoContaStore(state => state.setSelecaoGasto);
  const [gastoExpandidoId, setGastoExpandidoId] = useState<number | null>(null);
  const reversoesVinculadas = new Set<number>();

  const queryExclusaoNota = useMutation({
    mutationFn: async (idMovimentacao: number) => {
      const response = await chamarApi(endpoints.excluirVinculo, { id: idMovimentacao.toString() }, undefined);

      if (!response.sucesso) {
        throw new Error("Erro ao remover a nota fiscal");
      }

      setSelecaoGasto(idMovimentacao, false);
      props.onExcluirNota();
    },
    onError: error => {
      console.error(error);
    },
  });

  const queryExclusaoReversao = useMutation({
    mutationFn: async (idReversao: number) => {
      const response = await chamarApi(endpoints.excluirReversaoMovimentacao, { id: idReversao.toString() }, undefined);

      if (!response.sucesso) {
        throw new Error("Erro ao remover a vínculo da reversao da nota fiscal");
      }

      setMovimentacoesReversao(null);
      props.onExcluirReversao();
    },
    onError: error => {
      console.error(error);
    },
  });

  // Função para selecionar todos
  const handleSelectAll = () => {
    if (props.situacaoPc === SituacaoPrestacaoConta.Realizada) {
      return;
    }

    const movimentacoesComNota = props.gastos?.filter(gasto => gasto.notaFiscal) ?? [];
    const movimentacoesComNotaSelecionados = movimentacoesComNota.filter(gasto => gastosSelecionados.has(gasto.id));
    const algunsEstaoSelecionados = movimentacoesComNotaSelecionados.length > 0;

    for (const gasto of movimentacoesComNota) {
      if (gasto.id && gasto.valorTransacaoReal === gasto.notaFiscal?.valor) {
        setSelecaoGasto(gasto.id, !algunsEstaoSelecionados);
      }
    }
  };

  if (!props.gastos) {
    return null;
  }

  const tratarSelecaoNotaImportacao = (notaFiscal: PayloadCriarNF) => {
    if (movimentacaoSelecionada !== null) {
      setSelecaoGasto(movimentacaoSelecionada.id, true);
      setOperacao(null);
      props.onVincularNotaImportacao(notaFiscal);
    }
  };

  const tratarVincularReversao = (movimentacaoId: number, movimentacaoReversao: Movimentacao) => {
    setMovimentacoesReversao(null);
    const reversao: Omit<PayloadCriarReversaoMovimentacao, "limitecartao_id"> = {
      movimentacaoId,
      reversaoId: movimentacaoReversao.id,
      revertidoEm: movimentacaoReversao.dataTransacao,
    };
    props.onVincularReversao(reversao);
  };

  const tratarCriacaoNotaManual = (nota: PayloadCriarNF) => {
    if (movimentacaoSelecionada !== null) {
      setSelecaoGasto(movimentacaoSelecionada.id, true);
      setOperacao(null);
      props.onCriarNotaManual(nota);
    }
  };

  const tratarAlteracaoNota = (nota: NotaFiscal) => {
    if (movimentacaoSelecionada !== null) {
      setSelecaoGasto(movimentacaoSelecionada.id, true);
      setOperacao(null);
      props.onAlterarNota(nota);
    }
  };

  const handleVincularNota = (tipoNota: TipoNotaFiscal, movimentacao: Movimentacao) => {
    setMovimentacaoSelecionada(movimentacao);

    if (tipoNota === TipoNotaFiscal.reversao) {
      const valorParaReversao = movimentacao.valorTransacaoReal;
      const dataParaReversao = movimentacao.dataTransacao;
      const movRev = props.gastos?.filter(
        d =>
          d.codigoTransacaoBB === 253600 &&
          d.valorTransacaoReal * -1 === valorParaReversao &&
          d.dataTransacao >= dataParaReversao &&
          !reversoesVinculadas.has(d.id),
      );
      setMovimentacoesReversao(movRev ?? null);
      return;
    }

    if (tipoNota === TipoNotaFiscal.notaFiscalEletronicaImportacao) {
      setOperacao({ tipoOperacao: TipoOperacaoTabelaGastos.AdicionarImportacao, tipoNota });
    } else {
      setOperacao({ tipoOperacao: TipoOperacaoTabelaGastos.AdicionarManual, tipoNota });
    }
  };

  const excluirNota = (movimentacao: Movimentacao) => {
    queryExclusaoNota.mutate(movimentacao.id);
  };

  const excluirReversao = (reversaoMovimentacao: ReversaoMovimentacao) => {
    queryExclusaoReversao.mutate(reversaoMovimentacao.id);
  };

  const editarNota = (movimentacao: Movimentacao) => {
    if (movimentacao.notaFiscal === null) {
      return;
    }

    setMovimentacaoSelecionada(movimentacao);
    const tipoNota = movimentacao.notaFiscal.tiponotafiscal_id;

    if (tipoNota === TipoNotaFiscal.notaFiscalEletronicaImportacao) {
      setOperacao({ tipoOperacao: TipoOperacaoTabelaGastos.EditarImportacao, tipoNota });
    } else {
      setOperacao({ tipoOperacao: TipoOperacaoTabelaGastos.EditarManual, tipoNota });
    }
  };

  const tipoNotaTemplate = (rowData: Movimentacao) => {
    if (rowData.notaFiscal === null && rowData.reversao === null) {
      return "-";
    }

    const tipoNota = rowData.reversao?.criadoem ? TipoNotaFiscal.reversao : rowData.notaFiscal?.tiponotafiscal_id;

    const getTipoNota = () => {
      if (tipoNota == TipoNotaFiscal.notaFiscalEletronicaImportacao) {
        return { tipoTitle: "Nota Fiscal Eletrônica Importada Automática", tipoString: "NF-e", tipoLabel: "A" };
      } else if (tipoNota == TipoNotaFiscal.notaFiscalEletronicaManual) {
        return { tipoTitle: "Nota Fiscal Eletrônica Digitada Manual", tipoString: "NF-e", tipoLabel: "M" };
      } else if (tipoNota == TipoNotaFiscal.notaServicoManual) {
        return { tipoTitle: "Nota Fiscal de Serviço Digitada Manual", tipoString: "NF-s", tipoLabel: "M" };
      }
      if (tipoNota == TipoNotaFiscal.cupomFiscalManual) {
        return { tipoTitle: "Cupom Fiscal Digitado Manual", tipoString: "CF", tipoLabel: "M" };
      }
      if (tipoNota == TipoNotaFiscal.reversao) {
        return { tipoTitle: "Nota/Cupom Fiscal Revertido", tipoString: "Revertida", tipoLabel: "R" };
      } else {
        return { tipoTitle: "Erro na identificação da Nota", tipoString: "Erro", tipoLabel: "E" };
      }
    };

    const { tipoTitle, tipoString, tipoLabel } = getTipoNota();

    return (
      <>
        <span title={tipoTitle}>{tipoString}</span>
        <Tag
          className="ml-2"
          severity={tipoLabel == "A" ? "success" : tipoLabel == "R" ? "info" : "warning"}
          value={tipoLabel}
        />
      </>
    );
  };

  const notaFiscalTemplate = (rowData: DadosLinhaTabela) => {
    const templateSelecaoNota = () => {
      const ptDropDown = {
        panel: {
          className: estilo.dropDown,
        },
        input: {
          className: estilo.dropDown,
        },
      };
      return (
        <Dropdown
          value={null}
          onChange={(e: DropdownChangeEvent) => handleVincularNota(e.value as TipoNotaFiscal, rowData)}
          options={opcoesNotaFiscal}
          optionLabel="descricao"
          optionValue="codigo"
          placeholder="Vincular Nota ou Reversão"
          title="Vincular Nota/Cupom Fiscal ou Reversão"
          pt={ptDropDown}
        />
      );
    };

    const reversaoTemplate = () => {
      return (
        rowData.reversao !== null && (
          <div className="card flex justify-content-start gap-2">
            <span>Data da Reversão : {formatarDataHora(rowData.reversao.revertidoEm)}</span>
            <Button
              onClick={() => {
                confirmDialog({
                  message: "Você tem certeza que deseja remover o vínculo com a Reversão desta Nota Fiscal?",
                  header: "Confirmação",
                  icon: "pi pi-exclamation-triangle",
                  acceptLabel: "Excluir",
                  rejectLabel: "Cancelar",
                  acceptClassName: telaPadrao.botaoPrincipal,
                  rejectClassName: estilo.botaoCancelar,
                  accept: () => {
                    if (rowData.reversao !== null) {
                      excluirReversao(rowData.reversao);
                    }
                  },
                });
              }}
              title="Remover vínculo com a Reversão desta Nota Fiscal"
            >
              {props.situacaoPc !== SituacaoPrestacaoConta.Realizada && <i className="pi pi-trash" />}
            </Button>
          </div>
        )
      );
    };

    if (rowData.atualizando) {
      return <SpinnerCpesc style={{ width: "24px", height: "24px" }} />;
    }

    if (rowData.reversao) {
      return reversaoTemplate();
    }

    if (rowData.codigoTransacaoBB === 253600) {
      if (reversoesVinculadas.has(rowData.id)) {
        return <b>REVERSÃO DE PAGAMENTO (vinculada)</b>;
      }
      return <>REVERSÃO DE PAGAMENTO</>;
    }

    if (!rowData.notaFiscal && props.situacaoPc === SituacaoPrestacaoConta.Realizada) {
      return <i className="pi pi-ban" title="Nota Fiscal não encontrada." />;
    }

    if (rowData.codigoTransacaoBB === 253600) {
      if (reversoesVinculadas.has(rowData.id)) {
        return <b>REVERSÃO DE PAGAMENTO (vinculada)</b>;
      }
      return <>REVERSÃO DE PAGAMENTO</>;
    }

    if (!rowData.notaFiscal) {
      return templateSelecaoNota();
    }

    const expandido = gastoExpandidoId === rowData.id;

    return (
      <div className={estilo.containerChevron}>
        <Button
          link
          onClick={() => {
            setGastoExpandidoId(expandido ? null : rowData.id);
          }}
        >
          <i className={`pi ${expandido ? "pi-chevron-up" : "pi-chevron-down"}`} />
          {rowData.notaFiscal.numeroserie}
        </Button>
        {props.situacaoPc !== SituacaoPrestacaoConta.Realizada && (
          <>
            <Button
              onClick={() => {
                confirmDialog({
                  message: "Você tem certeza que deseja remover o vínculo com a Nota Fiscal?",
                  header: "Confirmação",
                  icon: "pi pi-exclamation-triangle",
                  acceptLabel: "Excluir",
                  rejectLabel: "Cancelar",
                  acceptClassName: telaPadrao.botaoPrincipal,
                  rejectClassName: estilo.botaoCancelar,
                  accept: () => excluirNota(rowData),
                });
              }}
              title="Remover vínculo com Nota Fiscal"
            >
              <i className="pi pi-trash" />
            </Button>
            <Button
              onClick={() => editarNota(rowData)}
              title={"Editar o vínculo com Nota Fiscal"}
            >
              <i className="pi pi-pencil" />
            </Button>
            {rowData.notaFiscal.valor !== rowData.valorTransacaoReal && (
              <Button
                title="Esta nota apresenta valores diferentes da compra realizada"
                className={estilo.botaoNaTabelaComErro}
              >
                <i className={classNames("pi pi-exclamation-triangle")} />
              </Button>              
            )}
          </>
        )}
      </div>
    );
  };

  const rowExpansionTemplate = (data: Movimentacao) => {
    if (!data.notaFiscal?.dataemissao) return null;

    const rodapeExpandido = () => {
      return (
        <div className={estilo.impostos}>
          {(data.notaFiscal?.cofins ?? 0) > 0 && <>COFINS: {formatarMoeda(data.notaFiscal?.cofins ?? 0)} - </>}
          {(data.notaFiscal?.iss ?? 0) > 0 && <>ISS: {formatarMoeda(data.notaFiscal?.iss ?? 0)} - </>}
          {(data.notaFiscal?.pis ?? 0) > 0 && <>PIS: {formatarMoeda(data.notaFiscal?.pis ?? 0)} - </>}
          {data.notaFiscal?.inss != null && data.notaFiscal.inss > 0 && (
            <>INSS: {formatarMoeda(data.notaFiscal.inss)} - </>
          )}
          {(data.notaFiscal?.ir ?? 0) > 0 && <>IR: {formatarMoeda(data.notaFiscal?.ir ?? 0)} - </>}
        </div>
      );
    };
    const colunasNota: ColunasMeta[] = [
      {
        key: "ncm",
        header: "NCM - Descrição",
        body: (item: NotaFiscalItem) => (item.descricao ? `${item.ncm} - ${item.descricao}` : "NCM não encontrado"),
      },
      { key: "quantidade", header: "Quant.", field: "quantidade" },
      { key: "unidade", header: "Uni.", field: "unidade" },
      {
        key: "valorUnitario",
        header: "V. Unitário",
        body: (rowData: NotaFiscalItem) => formatarMoeda(rowData.valorunitario),
      },
      {
        key: "valor",
        header: "Valor",
        body: (rowData: NotaFiscalItem) => formatarMoeda(rowData.valor),
      },
    ];

    return (
      <div className={estilo.notaFiscalExpanded}>
        <div className={estilo.notaFiscalHeader}>
          <h3>Nota Fiscal - {data.notaFiscal.numeroserie}</h3>
          <Button icon="pi pi-times" rounded text severity="secondary" onClick={() => setGastoExpandidoId(null)} />
        </div>
        <div className={estilo.notaFiscalInfo}>
          <span>Valor: {formatarMoeda(data.notaFiscal.valor)}</span>
          <span>Data de emissão: {formatarData(data.notaFiscal.dataemissao)}</span>
        </div>
        <DataTable
          value={data.notaFiscal.notafiscalitens ?? []}
          responsiveLayout="stack"
          breakpoint="640px"
          emptyMessage="Nenhum item encontrado nesta nota."
          footer={rodapeExpandido}
        >
          {colunasNota.map(col => (
            <Column key={col.key} field={col.field} header={col.header} body={col.body} />
          ))}
        </DataTable>
      </div>
    );
  };

  const checkboxTemplate = (rowData: DadosLinhaTabela) => {
    return (
      <Checkbox
        checked={rowData.selecionado}
        disabled={rowData.notaFiscal === null || props.situacaoPc === SituacaoPrestacaoConta.Realizada || rowData.notaFiscal.valor !== rowData.valorTransacaoReal}
        onChange={e => {
          if (rowData.notaFiscal) {
            setSelecaoGasto(rowData.id, e.checked ?? false);
          }
        }}
      />
    );
  };

  const headerCheckboxTemplate = (transpor: boolean) => {
    const movimentacoesComNota = props.gastos?.filter(gasto => gasto.notaFiscal) ?? [];
    const movimentacoesComNotaSelecionados = movimentacoesComNota.filter(gasto => gastosSelecionados.has(gasto.id));
    const todosEstaoSelecionados = movimentacoesComNota.length === movimentacoesComNotaSelecionados.length;
    const algunsEstaoSelecionados = movimentacoesComNotaSelecionados.length > 0;

    let classButton = "pi pi-circle-off";
    let titleButton = "Todos Desmarcados - clique para selecionar/desmarcar todos";

    if (todosEstaoSelecionados) {
      classButton = "pi pi-check-circle";
      titleButton = "Todos Selecionados - clique para desmarcar/selecionar todos";
    } else if (algunsEstaoSelecionados) {
      classButton = "pi pi-minus-circle";
      titleButton = "Alguns Selecionados - clique para desmarcar/selecionar todos";
    }

    return (
      <div className={classNames("flex align-items-center", !transpor && "justify-content-center")}>
        <div className="ml-1" style={{ fontSize: "0.8rem", color: "var(--primary-color)" }}>
          <i className={classButton} title={titleButton} onClick={handleSelectAll} />
        </div>
      </div>
    );
  };

  const dadosTabela = props.gastos.map((gasto): DadosLinhaTabela => {
    const gastoSelecionado = gastosSelecionados.has(gasto.id);
    if (gasto.reversao !== null) {
      reversoesVinculadas.add(gasto.reversao.reversaoId); //Ver reversoes
    }
    return {
      ...gasto,
      valorTransacaoReal: gasto.valorTransacaoReal,
      selecionado: gastoSelecionado,
      atualizando: queryExclusaoNota.isPending && queryExclusaoNota.variables === gasto.id,
    };
  });

  return (
    <>
      <div className={estilo.linha}>
        <h2>Gastos</h2>
      </div>
      <hr />
      <div className={estilo.tabela}>
        <Tabela
          dados={dadosTabela}
          breakpoint={896}
          linhaExpandida={rowData => gastoExpandidoId === rowData.id && rowExpansionTemplate(rowData)}
          colunas={[
            {
              cabecalho: headerCheckboxTemplate,
              conteudo: checkboxTemplate,
            },
            { cabecalho: "Data Gasto", conteudo: (rowData: DadosLinhaTabela) => formatarData(rowData.dataTransacao) },
            { cabecalho: "Estabelecimento", conteudo: (rowData: DadosLinhaTabela) => rowData.nomeEstabelecimento },
            {
              cabecalho: "CNPJ",
              conteudo: (rowData: DadosLinhaTabela) => aplicarMascaraCNPJ(rowData.CNPJEstabelecimento),
            },
            { cabecalho: "Cidade", conteudo: (rowData: DadosLinhaTabela) => rowData.cidadeEstabelecimento },
            { cabecalho: "UF", conteudo: (rowData: DadosLinhaTabela) => rowData.ufEstabelecimento },
            { cabecalho: "Tipo", conteudo: tipoNotaTemplate },
            { cabecalho: "Documento Fiscal", conteudo: notaFiscalTemplate },
            { cabecalho: "Valor", conteudo: (rowData: DadosLinhaTabela) => formatarMoeda(rowData.valorTransacaoReal) },
          ]}
          mensagemSemDados="Nenhum gasto encontrado"
        />
      </div>
      {movimentacaoSelecionada && (
        <>
          <AdicaoNotaFiscalImportacao
            movimentacaoId={movimentacaoSelecionada.id}
            email={usuario.email}
            chave={movimentacaoSelecionada.notaFiscal?.chave ?? ""}
            operacao={operacao}
            onSelecionar={tratarSelecaoNotaImportacao}
            onFechar={() => {
              setOperacao(null);
              setMovimentacaoSelecionada(null);
            }}
          />
          {(operacao?.tipoOperacao === TipoOperacaoTabelaGastos.AdicionarManual ||
            operacao?.tipoOperacao === TipoOperacaoTabelaGastos.EditarManual) && (
            <AdicaoNotaFiscalManual
              movimentacaoId={movimentacaoSelecionada.id}
              totalMovimentacao={movimentacaoSelecionada.valorTransacaoReal}
              operacao={operacao}
              onCriar={tratarCriacaoNotaManual}
              onAlterar={tratarAlteracaoNota}
              onFechar={() => {
                setOperacao(null);
                setMovimentacaoSelecionada(null);
              }}
            />
          )}
          {movimentacoesReversao !== null && (
            <VincularReversao
              movimentacaoId={movimentacaoSelecionada.id}
              movimentacoes={movimentacoesReversao}
              onSelecionar={tratarVincularReversao}
              onFechar={() => setMovimentacoesReversao(null)}
            />
          )}
        </>
      )}
    </>
  );
}
export default TabelaGastos;
