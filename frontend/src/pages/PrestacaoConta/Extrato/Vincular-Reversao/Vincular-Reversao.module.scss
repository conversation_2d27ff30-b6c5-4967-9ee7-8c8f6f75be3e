@use "@/variables.scss";

.painelBusca {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  font-size: 0.8rem;
  padding: 2rem 0;
  @media only screen and (max-width: 768px) {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
}

.colunaCampos {
  display: flex;
  column-gap: 0.5rem;
  row-gap: 0.1rem;
  width: 100%;

  &:not(:first-child):not(:last-child) {
    width: fit-content;
  }

  @media only screen and (max-width: 768px) {
    align-self: center;
    &:not(:first-child):not(:last-child) {
      width: 100%;
    }
  }
}

.colunaTitulo {
  display: flex;
  align-items: flex-end;
  flex-direction: column;
  row-gap: 1.3rem;
}

.colunaInput {
  display: flex;
  flex-direction: column;
  row-gap: 0.5rem;
}

.caixaInput {
  background-color: white;
  padding: 5px;
  font-size: 0.8rem;
  border-radius: var(--border-radius-padrao);
}

.caixaChave {
  background-color: white;
  padding: 5px;
  font-size: 0.8rem;
  border-radius: var(--border-radius-padrao);
  height: 26px;
}

.divisor {
  @media screen and (max-width: 768px) {
    margin: 1rem 0;
    padding: 0 1rem;
    justify-content: center;
    width: 100%;
    &::before {
      align-content: center;
      border-top: 1px solid var(--cor-linha);
      top: 50%;
      left: 0;
      height: inherit;
      width: 100%;
    }
    & div {
      padding: 0 0.5rem;
    }
  }
}

.painelBotao {
  display: flex;
  gap: 10px;
  justify-content: right;
}

.tabela {
  td {
    &:last-child,
    &:nth-last-child(2) {
      text-align: right;
    }
    & button {
      margin: inherit;
    }
  }
}

.formContainer {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
  padding: 1.5rem 0;

  @media only screen and (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
}

.formSection {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  &:first-child {
    flex: 0 0 38.2%; // Primeira seção ocupará menor espaço
  }

  &:last-child {
    flex: 1; // Última seção ocupará o espaço restante
  }
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;

  label {
    font-weight: 600;
    font-size: 0.9rem;
    color: #333;
  }
}

.divider {
  display: flex;
  align-items: center;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 1px;
    background-color: #ddd;
  }

  @media only screen and (max-width: 768px) {
    width: 100%;
    margin: 1rem 0;

    &::before {
      width: 100%;
      height: 1px;
      left: 0;
    }
  }
}

.input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  width: 100%;

  &:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
  }
}

.mensagemInferior {
  min-width: 99%;
  bottom: 1rem;

  :global(.p-message) {
    margin: 1rem;
  }
}
