import Breadcrumb from "@/components/Breadcrumb/Breadcrumb";
import TelaCarregando from "@/components/TelaCarregando";
import { useAuth } from "@/contexts/auth/useAuth";
import { chamarApi } from "@/helpers/api";
import { getIdPrestacaoContas, getSituacaoCredito } from "@/helpers/credito";
import { filters } from "@/helpers/filters";
import { aplicarMascaraCartao, formatarMoeda } from "@/helpers/formatacao";
import { useMutationAlterarNota } from "@/hooks/useMutationAlterarNota";
import { useMutationCriarNota } from "@/hooks/useMutationCriarNota";
import { useMutationCriarReversao } from "@/hooks/useMutationCriarReversao";
import { useToast } from "@/hooks/useToast";
import { useExtratoPrestacaoContaStore } from "@/stores/ExtratoPrestacaoContaStore";
import telaPadrao from "@/tela-padrao.module.scss";
import { useSuspenseQuery } from "@tanstack/react-query";
import {
  type Credito,
  type CreditoCartao,
  type Movimentacao,
  Perfil,
  SituacaoPrestacaoConta,
  type UsuarioAutenticado,
} from "cpesc-shared";
import { endpoints } from "cpesc-shared/out/endpoints/main";
import type { PayloadCriarNF } from "cpesc-shared/out/endpoints/nota-fiscal";
import type { PayloadCriarReversaoMovimentacao } from "cpesc-shared/out/endpoints/reversao-movimentacao";
import { Button } from "primereact/button";
import { Card } from "primereact/card";
import { Checkbox } from "primereact/checkbox";
import { Dialog } from "primereact/dialog";
import { InputText } from "primereact/inputtext";
import { Message } from "primereact/message";
import { Suspense, useEffect, useState } from "react";
import { Navigate, useParams } from "react-router-dom";
import estilo from "./ExtratoPrestacaoConta.module.scss";
import { type DadosFinalizarCredito, finalizarCredito, imprimirExtrato } from "./FinalizarCredito";
import { TabelaCredito } from "./Tabelas/TabelaCredito";
import { TabelaGastos } from "./Tabelas/TabelaGastos";

function montarDadosFinalizarCredito(
  credito: Credito,
  movimentacoes: Movimentacao[],
  usuario: UsuarioAutenticado,
): DadosFinalizarCredito {
  const { situacao, orgao, processo, ano, gastosSelecionados } = useExtratoPrestacaoContaStore.getState();

  if (situacao === null) {
    throw new Error("Situação da prestação de contas não definida.");
  }

  const movimentacoesSelecionadas = movimentacoes.filter(m => gastosSelecionados.has(m.id));
  const totalSelecionados = getTotalSelecionados(credito, movimentacoesSelecionadas, situacao);

  return {
    credito,
    movimentacoes: movimentacoesSelecionadas,
    totalGastos: totalSelecionados,
    orgao,
    processo,
    ano: ano?.toString() ?? "",
    usuario,
  };
}

function getTotalSelecionados(
  credito: CreditoCartao,
  movimentacoesSelecionadas: Movimentacao[],
  situacao: SituacaoPrestacaoConta,
): number {
  if (situacao !== SituacaoPrestacaoConta.Realizada) {
    return movimentacoesSelecionadas.reduce((total, m) => {
      return total + m.valorTransacaoReal;
    }, 0);
  }

  return credito.prestacao.reduce((acc, p) => acc + p.valorGasto, 0);
}

export default function ExtratoPrestacaoConta() {
  const { usuario } = useAuth();
  const params = useParams();
  const idCreditoSelecionado: number = parseInt(params.id ?? "0");
  const situacao = useExtratoPrestacaoContaStore(state => state.situacao);
  const creditoSelecionado = useExtratoPrestacaoContaStore(state => state.creditoSelecionado);
  const setSituacao = useExtratoPrestacaoContaStore(state => state.setSituacao);
  const setOrgao = useExtratoPrestacaoContaStore(state => state.setOrgao);
  const setProcesso = useExtratoPrestacaoContaStore(state => state.setProcesso);
  const setAno = useExtratoPrestacaoContaStore(state => state.setAno);
  const setCreditoSelecionado = useExtratoPrestacaoContaStore(state => state.setCreditoSelecionado);
  const setSelecaoGasto = useExtratoPrestacaoContaStore(state => state.setSelecaoGasto);
  const limparGastosSelecionados = useExtratoPrestacaoContaStore(state => state.limparGastosSelecionados);

  const [movimentacoes, setMovimentacoes] = useState<Movimentacao[]>([]);
  const [confirmacaoZeramento, setConfirmacaoZeramento] = useState(false);
  const [confirmacaoCancelamento, setConfirmacaoCancelamento] = useState(false);
  const [dialogConfirmFinalizar, setDialogConfirmFinalizar] = useState<boolean>(false);
  const [dialogConfirmaCancelamento, setDialogConfirmaCancelamento] = useState<boolean>(false);
  const [motivoCancelamento, setMotivoCancelamento] = useState<string>("");
  const toast = useToast();

  const { data, isFetching, refetch } = useSuspenseQuery({
    queryKey: ["/limite-cartao/gastos/", idCreditoSelecionado],
    queryFn: () => chamarApi(endpoints.buscarGastosCartao, { idCredito: idCreditoSelecionado.toString() }),
  });

  const mutationCriarNota = useMutationCriarNota();
  const mutationAtualizarNota = useMutationAlterarNota();
  const mutationCriarReversao = useMutationCriarReversao();

  useEffect(() => {
    if (!idCreditoSelecionado) {
      toast.show({
        severity: "error",
        summary: "Crédito inválido",
        detail: "Não é possível exibir extrato sem um crédito válido selecionado.",
        life: 5000,
      });
    }
  }, [idCreditoSelecionado, toast]);

  useEffect(() => {
    const situacaoPc = getSituacaoCredito(data);
    const novasMovimentacoes =
      situacaoPc === SituacaoPrestacaoConta.Realizada ? (data.movimentacoes ?? []) : data.cartao.movimentacoes;
    limparGastosSelecionados();
    const reversoesVinculadas = novasMovimentacoes.map(m => m.reversao?.reversaoId).filter(filters.isNotUndefined);

    for (const gasto of novasMovimentacoes) {
      if (situacaoPc === SituacaoPrestacaoConta.Realizada || gasto.reversao || reversoesVinculadas.includes(gasto.id) || gasto.valorTransacaoReal === gasto.notaFiscal?.valor ) {
        setSelecaoGasto(gasto.id, true);
      }
    }
    // Atualiza processo SGPE
    if (data.prestacao.length > 0) {
      setAno(parseInt(data.prestacao[0].anoSgpe));
      setOrgao(data.prestacao[0].orgaoSgpe);
      setProcesso(data.prestacao[0].processoSgpe);
    } else if (creditoSelecionado?.id !== idCreditoSelecionado) {
      const anoCorrente = new Date().getFullYear();
      setAno(anoCorrente);
      setOrgao("");
      setProcesso("");
    }

    // Atualiza movimentações
    setMovimentacoes(novasMovimentacoes);

    // Atualiza crédito selecionado por último
    setCreditoSelecionado(data);
    setSituacao(situacaoPc);

  }, [data]);

  if (!idCreditoSelecionado) {
    return <Navigate to="/prestacao-contas/selecao-credito" replace />;
  }

  if (isFetching || mutationAtualizarNota.isPending || creditoSelecionado === null || situacao === null) {
    return <TelaCarregando />;
  }

  const tratarVinculoReversao = (reversao: Omit<PayloadCriarReversaoMovimentacao, "limitecartao_id">): void => {
    mutationCriarReversao.mutate(
      { ...reversao, limitecartao_id: creditoSelecionado.id },
      {
        onSuccess: () => {
          void refetch();
          toast.show({
            severity: "success",
            summary: "Gasto vinculado a reversão.",
            detail: "O gasto foi vinculado com sucesso a reversão.",
            life: 5000,
          });
        },
        onError: error => {
          toast.show({
            severity: "error",
            summary: "Falha ao vincular a reversão",
            detail: "Não é possível vincular a reversão ao gasto.",
            life: 5000,
          });
          console.log("Erro ao criar reversão:", error);
        },
      },
    );
  };

  const tratarVinculoNota = (nota: PayloadCriarNF): void => {
    mutationCriarNota.mutate(nota, {
      onSuccess: response => {
        if (response.idNotaFiscalGerada) {
          void refetch();
        }
      },
    });
  };

  const queryCancelarPrestacao = async (motivoCancelamento: string) => {
    try {
      const prestacaoId = getIdPrestacaoContas(creditoSelecionado);
      const response = await chamarApi(
        endpoints.cancelarPrestacaoContas,
        {},
        {
          prestacaoId,
          motivoCancelamento,
        },
      );

      if (!response) {
        throw new Error("Erro ao remover a nota fiscal");
      }
      /*
      setSelecaoGasto(idMovimentacao, false); */
      void refetch();
    } catch (error) {
      console.error(error);
    }
  };

  const aceitarZeramento = async () => {
    setConfirmacaoZeramento(false);
    toast.show({
      severity: "info",
      summary: "Confirmado",
      detail: "Foi encaminhado o pedido de ZERAMENTO de limite do seu cartão.",
      life: 7000,
    });

    const dadosFinalizarCredito = montarDadosFinalizarCredito(creditoSelecionado, movimentacoes, usuario);

    if (await finalizarCredito(dadosFinalizarCredito)) {
      setSituacao(SituacaoPrestacaoConta.Realizada);
      void refetch();
    }
  };

  const rejeitarZeramento = () => {
    setConfirmacaoZeramento(false);
    toast.show({
      severity: "warn",
      summary: "AVISO",
      detail: "O crédito não foi finalizado e caso houver saldo, ainda será possível utilizá-lo.",
      life: 7000,
    });
  };

  const footerZeramento = (
    <div>
      <Button
        label="Não"
        icon="pi pi-times"
        onClick={() => {
          setDialogConfirmFinalizar(false);
          rejeitarZeramento();
        }}
        className={estilo.botaoCancelar}
      />
      <Button
        label={confirmacaoZeramento ? "Sim" : "Aguardando Aceite..."}
        icon="pi pi-check"
        onClick={() => {
          setDialogConfirmFinalizar(false);
          void aceitarZeramento();
        }}
        disabled={!confirmacaoZeramento}
        className={telaPadrao.botaoPrincipal}
      />
    </div>
  );

  const aceitarCancelamento = async () => {
    setConfirmacaoCancelamento(false);

    await queryCancelarPrestacao(motivoCancelamento);

    toast.show({
      severity: "info",
      summary: "Confirmado",
      detail: "Prestação de Contas liberada para edição.",
      life: 3000,
    });
  };

  const rejeitarCancelamento = () => {
    setConfirmacaoCancelamento(false);
    toast.show({
      severity: "warn",
      summary: "AVISO",
      detail: "Liberação para edição da prestação de contas não foi realizada.",
      life: 3000,
    });
  };

  const footerCancelamento = (
    <div>
      <Button
        label="Não"
        icon="pi pi-times"
        onClick={() => {
          setDialogConfirmaCancelamento(false);
          rejeitarCancelamento();
        }}
        className={estilo.botaoCancelar}
      />
      <Button
        label={
          confirmacaoCancelamento
            ? motivoCancelamento.length < 20
              ? "Preencha o motivo. "
              : "Sim"
            : "Aguardando ciência..."
        }
        icon="pi pi-check"
        onClick={() => {
          setDialogConfirmaCancelamento(false);
          void aceitarCancelamento();
        }}
        disabled={!confirmacaoCancelamento || motivoCancelamento.length < 20}
        className={telaPadrao.botaoPrincipal}
      />
    </div>
  );

  return (
    <main className={telaPadrao.container}>
      <section className={telaPadrao.tituloTela}>
        <h1>Demonstrativo para Prestação de Contas</h1>
        <Breadcrumb />
      </section>

      <section>
        <div className={estilo.declaracao}>
          <div className={estilo.info}>
            <label htmlFor="portador" className={estilo.label}>
              <span id="portador">Portador:</span>
              {creditoSelecionado.cartao.portadorUnidadeAdministrativa.portador.nome}
            </label>
            <label htmlFor="ug" className={estilo.label}>
              <span id="ug">Unidade Gestora:</span>
              <span
                style={{
                  display: "inline-block",
                  maxWidth: "30ch",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  verticalAlign: "bottom",
                  fontWeight: "normal",
                }}
                title={`${creditoSelecionado.cartao.portadorUnidadeAdministrativa.unidadeAdministrativa.unidadeGestora.descricao} - ${creditoSelecionado.cartao.portadorUnidadeAdministrativa.unidadeAdministrativa.unidadeGestora.nome}`}
              >
                {creditoSelecionado.cartao.portadorUnidadeAdministrativa.unidadeAdministrativa.unidadeGestora.descricao}
              </span>
            </label>
            <FormProcesso />
          </div>
          <GrandesNumeros credito={creditoSelecionado} movimentacoes={movimentacoes} />
        </div>

        <Suspense fallback={"Carregando dados..."}>
          <div className={telaPadrao.espaco} />
          <TabelaCredito credito={creditoSelecionado} />
          <div className={telaPadrao.espaco} />
          <TabelaGastos
            gastos={movimentacoes}
            situacaoPc={situacao}
            onVincularNotaImportacao={tratarVinculoNota}
            onCriarNotaManual={tratarVinculoNota}
            onAlterarNota={nota => {
              mutationAtualizarNota.mutate(nota, {
                onSuccess: response => {
                  if (response.sucesso) {
                    void refetch();
                  }
                },
              });
            }}
            onExcluirNota={() => void refetch()}
            onVincularReversao={tratarVinculoReversao}
            onExcluirReversao={() => void refetch()}
          />
          <div className={telaPadrao.espaco} />
          <div className={estilo.containerBotao}>
            {situacao === SituacaoPrestacaoConta.Realizada && (
              <>
                {(usuario.perfil === Perfil.AdministradorCiasc ||
                  usuario.perfil === Perfil.AdministradorCpesc ||
                  usuario.perfil === Perfil.GestorSed ||
                  usuario.perfil === Perfil.Gestor) && (
                  <Button
                    className={telaPadrao.botaoSecundario}
                    label="Liberar Edição"
                    tooltip="'Liberar Edição' permite que o Gestor cancele a prestação de contas para que o Portador possa editar os dados e finalizar novamente."
                    onClick={() => {
                      setDialogConfirmaCancelamento(true);
                    }}
                  ></Button>
                )}
                <Button
                  className={telaPadrao.botaoPrincipal}
                  label="Gerar Demonstrativo"
                  tooltip="Gerar um arquivo PDF com o Demonstrativo para Prestação de Contas"
                  onClick={() => {
                    const dadosFinalizarCredito = montarDadosFinalizarCredito(
                      creditoSelecionado,
                      movimentacoes,
                      usuario,
                    );
                    void imprimirExtrato(dadosFinalizarCredito);
                  }}
                ></Button>
              </>
            )}

            {situacao !== SituacaoPrestacaoConta.Realizada && (
              <BotaoFinalizarCredito onClick={() => setDialogConfirmFinalizar(true)} />
            )}
          </div>
        </Suspense>
      </section>

      <Dialog
        visible={dialogConfirmFinalizar}
        onHide={() => setDialogConfirmFinalizar(false)}
        header="AVISO"
        footer={footerZeramento}
        closable={false}
        className={estilo.dialogos}
      >
        <div className="flex flex-column align-items-center w-full gap-3">
          <div className="flex align-items-center">
            <Checkbox
              inputId="confirmaZeramento"
              checked={confirmacaoZeramento}
              onChange={e => setConfirmacaoZeramento(e.checked ?? false)}
            />
            <label htmlFor="confirmaZeramento" className="ml-2">
              Estou ciente que estou finalizando o crédito de <b>{formatarMoeda(creditoSelecionado.valorCredito)}</b> do
              cartão <b>{aplicarMascaraCartao(creditoSelecionado.cartao.numero, true)}</b>.<br /> Se houver saldo, não
              será mais possível utilizá-lo.
            </label>
          </div>
        </div>
        <span>
          <br />
          Tem certeza que deseja prosseguir?
        </span>
      </Dialog>

      <Dialog
        visible={dialogConfirmaCancelamento}
        onHide={() => setDialogConfirmaCancelamento(false)}
        header="AVISO"
        footer={footerCancelamento}
        className={estilo.dialogos}
        closable={false}
      >
        <div className="flex flex-column w-full gap-3">
          <div className="flex align-items-center">
            <Checkbox
              inputId="confirmaCancelamento"
              checked={confirmacaoCancelamento}
              onChange={e => setConfirmacaoCancelamento(e.checked ?? false)}
            />
            <label htmlFor="confirmaCancelamento" className="ml-2">
              Estou ciente que estou liberando para edição a prestação de contas do crédito do portador: <br />{" "}
              {creditoSelecionado.cartao.portadorUnidadeAdministrativa.portador.nome} de{" "}
              <b>{formatarMoeda(creditoSelecionado.valorCredito)}</b> do cartão{" "}
              <b>{aplicarMascaraCartao(creditoSelecionado.cartao.numero, true)}</b>.
            </label>
          </div>
          <div>
            <span>Qualquer uso do demonstrativo atual precisará ser revisto.</span>
          </div>
          <div>
            <label htmlFor="textoConfirmacao">
              Escreva uma justificativa para liberar para edição deste demonstrativo:
            </label>
          </div>
          <div>
            <InputText
              id="textoConfirmacao"
              name="motivo"
              className="w-full"
              placeholder="Digite uma justificativa com, no mínimo 20 caracteres..."
              onChange={e => setMotivoCancelamento(e.target.value)}
              value={motivoCancelamento}
            />
          </div>
          <div className={estilo.containerBotao}>
            <span>Tem certeza que deseja liberar para retificação esta prestação de contas?</span>
          </div>
        </div>
      </Dialog>
    </main>
  );
}

function FormProcesso() {
  const situacao = useExtratoPrestacaoContaStore(state => state.situacao);
  const orgao = useExtratoPrestacaoContaStore(state => state.orgao);
  const processo = useExtratoPrestacaoContaStore(state => state.processo);
  const ano = useExtratoPrestacaoContaStore(state => state.ano);
  const setOrgao = useExtratoPrestacaoContaStore(state => state.setOrgao);
  const setProcesso = useExtratoPrestacaoContaStore(state => state.setProcesso);
  const setAno = useExtratoPrestacaoContaStore(state => state.setAno);
  const somenteLeitura = situacao === SituacaoPrestacaoConta.Realizada;

  return (
    <label className={estilo.label}>
      <span>Processo:</span>
      <div className={`${estilo.caixainput} p-inputgroup flex-1`}>
        <InputText
          placeholder="Órgão"
          value={orgao}
          onChange={e => setOrgao(e.target.value.toUpperCase())}
          disabled={somenteLeitura}
          required
        />
        <InputText
          placeholder="Processo"
          value={processo}
          onChange={e => setProcesso(e.target.value.replace(/\D/g, ""))}
          disabled={somenteLeitura}
          required
        />
        <InputText
          placeholder="Ano"
          value={ano?.toString() ?? ""}
          onChange={e => {
            const novoAno = parseInt(e.target.value);

            if (isNaN(novoAno)) {
              setAno(null);
              return;
            }

            setAno(novoAno);
          }}
          disabled={somenteLeitura}
          required
        />
      </div>
    </label>
  );
}

function GrandesNumeros(props: { credito: CreditoCartao; movimentacoes: Movimentacao[] }) {
  const situacao = useExtratoPrestacaoContaStore(state => state.situacao);
  const gastosSelecionados = useExtratoPrestacaoContaStore(state => state.gastosSelecionados);

  if (situacao === null) {
    return null;
  }

  const movimentacoesSelecionadas = props.movimentacoes.filter(m => gastosSelecionados.has(m.id));
  const totalSelecionados = getTotalSelecionados(props.credito, movimentacoesSelecionadas, situacao);

  const ptcard = {
    root: {
      className: estilo.cartao,
    },
    subtitle: {
      className: estilo.cartaoSubTitulo,
    },
    content: {
      className: estilo.cartaoContent,
    },
  };

  return (
    <div className={estilo.grupoCartao}>
      <Card pt={ptcard} subTitle="Crédito">
        <p>{formatarMoeda(props.credito.valorCredito)}</p>
      </Card>
      <Card pt={ptcard} subTitle="Total de Gastos">
        <p>{formatarMoeda(totalSelecionados)}</p>
      </Card>
      <Card pt={ptcard} subTitle="Saldo">
        <p>{formatarMoeda(props.credito.valorCredito - totalSelecionados)}</p>
      </Card>
    </div>
  );
}

function BotaoFinalizarCredito(props: { onClick: () => void }) {
  const habilitado = useExtratoPrestacaoContaStore(state => {
    const camposPreenchidos = state.orgao !== "" && state.processo !== "" && state.ano !== null;

    if (!camposPreenchidos) {
      return false;
    }

    const movimentacoes = state.creditoSelecionado?.cartao.movimentacoes ?? [];
    if (state.creditoSelecionado?.temMaisLimites === false) {
      for (const movimentacao of movimentacoes) {
        if (movimentacao.notaFiscal === null || movimentacao.notaFiscal.valor !== movimentacao.valorTransacaoReal) { //TODO por em outro lugar
          return false;
        }
      }

      if (state.gastosSelecionados.size !== movimentacoes.length) {
        return false;
      }
    }

    return true;
  });

  const temMaisLimites = useExtratoPrestacaoContaStore(state => state.creditoSelecionado?.temMaisLimites);
  const temMovimentacoes = useExtratoPrestacaoContaStore(state => {
    const movimentacoes = state.creditoSelecionado?.cartao.movimentacoes ?? [];
    return movimentacoes.length > 0;
  });
  const mensagem = temMaisLimites
    ? "Você deve preencher Órgão, Processo e Ano para finalizar o crédito. VOCÊ POSSUI MAIS DE UM CRÉDITO ABERTO NESTE PERÍODO."
    : temMovimentacoes
      ? "Você deve preencher Órgão, Processo e Ano, vincular todas notas fiscais e selecionar todos os gastos para finalizar o crédito."
      : "Você deve preencher Órgão, Processo e Ano para finalizar o crédito.";

  return (
    <>
      <Message severity="warn" text={mensagem} style={{ flex: 1 }} />
      <Button
        className={telaPadrao.botaoPrincipal}
        style={{ flex: "0 0 auto" }}
        label="Finalizar Crédito"
        onClick={props.onClick}
        disabled={!habilitado}
      />
    </>
  );
}
