@use "@/mixins.scss";

@keyframes piscarSombra {
  0% {
    box-shadow: 0 0 1px 1px rgba(255, 0, 0, 0.8);
  }
  40% {
    box-shadow: 0 0 4px 4px rgba(255, 0, 0, 0.3);
  }
  80% {
    box-shadow: 0 0 1px 1px rgba(255, 0, 0, 0.8);
  }
  100% {
    box-shadow: none;
  }
}

@keyframes piscarCor {
  0% {
    color: rgba(255, 0, 0, 0.8);
    font-weight: lighter;
  }
  50% {
    color: rgba(1, 116, 53, 1);
    font-weight: bolder;
  }
  100% {
    color: rgba(255, 0, 0, 0.8);
    font-weight: lighter;
  }
}

.grupoCartao {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  width: 100%;
  gap: 1rem;
  @media (min-width: 768px) {
    flex-wrap: nowrap;
  }
}

.cartao {
  min-width: 210px;
}

.cartaoSubTitulo {
  text-align: center;
}

.cartaoContent {
  font-size: 2em;
  text-align: center;
  padding: 0.5rem 0 0;
}

.declaracao {
  display: flex;
  gap: 2rem;
  align-items: center;

  @media (max-width: 1023px) {
    flex-direction: column;
    gap: 0.5rem;
  }
}

.info {
  display: flex;
  flex-direction: column;
  width: 100%;

  @media (min-width: 1024px) {
    width: 70%;
  }
  @media (min-width: 1440px) {
    width: 60%;
  }
}

.label {
  display: inline-flex;
  gap: 1em;
  align-items: center;
  margin-bottom: 1em;
  flex-wrap: wrap;
  & > span {
    font-weight: bold;
    justify-self: flex-end;
    text-align: end;
  }

  @media (max-width: 640px) {
    gap: 0.25em;
    & > span {
      justify-self: flex-start;
    }
  }
}

.linha {
  display: flex;
  flex-direction: row;
  gap: 0.5rem;
  align-items: center;
  justify-content: space-between;
  & h2 {
    margin: 0;
  }
}

.caixainput {
  & input {
    background-color: white;
    padding: 5px;
    font-size: 1.2em;
    max-width: 6rem;
    transition: border-color 0.3s ease;
  }

  & input:not(:first-child):not(:last-child) {
    min-width: 75px;
  }
  & input:last-child {
    min-width: 46px;
  }
  & input:first-child {
    min-width: 55px;
  }

  & input:required:invalid {
    animation: piscarSombra 4s infinite;
  }

  & input:disabled {
    color: black;
    border-color: black;
    background-color: white;
  }
}

.containerBotao {
  align-items: center;
  display: flex;
  justify-content: end;

  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.containerChevron {
  display: flex;
  justify-content: start;
  align-items: center;
  gap: 5px;

  & button i {
    padding-right: 5px;
  }
}

.totalSelecionado {
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-padrao);
  font-weight: bold;
}

.notaFiscalExpanded {
  padding: 1rem;
  background-color: #f8f9fa;
  //background-color: var(--cor-fundo-padrao);
  border-radius: var(--border-radius-padrao);
  //box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  margin: 0.5rem 0;

  .notaFiscalHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    h3 {
      margin: 0;
    }
  }

  .notaFiscalInfo {
    display: flex;
    gap: 2rem;
    margin-bottom: 1rem;

    span {
      font-weight: 500;
    }
  }

  .tituloConfirmacao {
    color: var(--cor-texto-destacado);
  }

  @media (max-width: 768px) {
    .tabela :global(.p-datatable .p-datatable-tbody > tr > td) {
      background-color: var(--cor-verde-background);
    }
    .dropDown {
      font-size: 0.8rem;
    }
  }
}

.botaoCancelar {
  background-color: transparent;
  color: var(--cor-verde-escuro);
  border: var(--cor-verde-escuro);
  padding: 0 10px;
}

.botaoNaTabela {
  background-color: transparent;
  color: var(--cor-verde-escuro);
  border: var(--cor-verde-escuro);
  padding: 0;
}

.botaoNaTabelaComErro {
  @extend .botaoNaTabela;

  & i {
    animation: piscarCor 2s infinite;
  }
}

.impostos {
  font-weight: normal;
  text-align: right;
  font-size: small;
}

.dialogos {
  width: 40vw;
  @media (max-width: 768px) {
    width: 90vw;
  }
}

.dropDown {
  width: auto;
  font-weight: normal;
  @media (max-width: 768px) {
    font-size: 0.8rem;
    width: 80%;
  }
}
