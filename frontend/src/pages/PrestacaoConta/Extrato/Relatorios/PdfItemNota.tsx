import { formatarValor } from "@/helpers/formatacao";
import { Document, Image, Page, StyleSheet, Text, View } from "@react-pdf/renderer";
import type { NotaFiscalItem } from "cpesc-shared";
import brasao from "../../assets/Brasao_de_Armas_Estado_SC_digital.png";

export interface PdfPrestacaoContasProps {
  dados: NotaFiscalItem[];
}

export function PdfItemNota(props: NotaFiscalItem[]) {
  const styles = StyleSheet.create({
    hSplit: {
      display: "flex",
      flexDirection: "row",
    },
    vSplit: {
      display: "flex",
      flexDirection: "column",
    },
    negrito: {
      fontFamily: "Times-Bold",
      fontWeight: "bold",
    },
    table: {
      display: "flex",
      flexDirection: "column",
      width: "100%",
    },
    row: {
      display: "flex",
      flexDirection: "row",
      width: "100%",
    },
    headerRow: {
      fontFamily: "Helvetica-Bold",
      textAlign: "center",
    },
    footerRow: {
      fontFamily: "Helvetica-Bold",
      textAlign: "center",
    },
    footerTotal: {
      textAlign: "right",
      fontSize: "10",
      fontFamily: "Helvetica-Bold",
      paddingRight: 2,
    },
    cell: {
      padding: 0,
      marginBottom: 1,
      marginTop: 1,
      marginLeft: 1,
      marginRight: 1,
      height: "24px",
    },
    backGray: {},
    backWhite: {},
    w8: { width: "8%" },
    w10: { width: "10%" },
    w12: { width: "12%" },
    w15: { width: "15%" },
    w20: { width: "20%" },
    w25: { width: "25%" },
    w30: { width: "30%" },
    w33: { width: "33%" },
    w34: { width: "34%" },
    w40: { width: "40%" },
    w48: { width: "48%" },
    w50: { width: "50%" },
    w60: { width: "60%" },
    w70: { width: "70%" },
    w80: { width: "80%" },
    w100: { width: "100%" },
    centralized: {
      textAlign: "center",
      alignContent: "center",
    },
    underline: {
      borderBottom: "1px",
      borderBottomColor: "#3e3e3e",
      borderBottomStyle: "solid",
    },
    value: {
      textAlign: "right",
    },
    pagina: {
      fontSize: 10,
      paddingTop: 60, // 3 cm
      paddingLeft: 60, // 3 cm
      paddingRight: 40, // 2 cm
      paddingBottom: 40,
    },
    secao: {
      display: "flex",
      flexDirection: "row",
    },
    brasao: {
      marginTop: 2,
      width: 60,
    },
    titulo: {
      fontSize: 18,
      marginTop: 5,
      marginLeft: 25,
    },
    subTitulo: {
      fontSize: 12,
      marginTop: 4,
      marginLeft: 25,
    },
    cabecalhoSecao: {
      fontSize: 14,
    },
    bordaPrimeiroCabecalho: {
      borderLeft: "1px",
      borderTop: "1px",
      borderRight: "1px",
      borderBottom: "1px",
    },
    bordaCabecalho: {
      borderLeft: "0px",
      borderTop: "1px",
      borderRight: "1px",
      borderBottom: "1px",
    },
    bordaPrimeiraCelula: {
      fontSize: 8,
      padding: 1,
      borderLeft: "1px",
      borderTop: "0px",
      borderRight: "1px",
      borderBottom: "1px",
    },
    bordaCelula: {
      fontSize: 8,
      padding: 1,
      borderLeft: "0px",
      borderTop: "0px",
      borderRight: "1px",
      borderBottom: "1px",
    },
    tabelaAssinaturas: {
      paddingTop: 16,
      margin: 0,
      padding: 0,
    },
    assinatura: {
      fontFamily: "Helvetica-Bold",
      paddingTop: 48,
      textAlign: "center",
    },
    localData: {
      fontFamily: "Helvetica-Bold",
      paddingTop: 48,
      textAlign: "right",
    },
  });

  return (
    <Document>
      <Page size="A4" style={styles.pagina}>
        <View fixed>
          <View style={styles.secao}>
            <Image src={brasao} style={styles.brasao} />
            <View>
              <Text style={styles.titulo}>Estado de Santa Catarina</Text>
              <Text style={styles.subTitulo}>
                Relatorio de Gastos por NCM{"\n"}
                Cartão de Pagamentos do Estado de Santa Catarina - CPESC
              </Text>
            </View>
          </View>
        </View>
        <View style={styles.table}>
          <View style={[styles.row, styles.headerRow]}>
            <View style={[styles.bordaPrimeiroCabecalho, styles.w12]}>
              <Text>NCM</Text>
            </View>
            <View style={[styles.bordaCabecalho, styles.w48]}>
              <Text>Descricao</Text>
            </View>
            <View style={[styles.bordaCabecalho, styles.w20]}>
              <Text>Quantidade</Text>
            </View>
            <View style={[styles.bordaCabecalho, styles.w20]}>
              <Text>Valor (R$)</Text>
            </View>
          </View>

          {props.map((g, i) => {
            return (
              <View key={i} style={[styles.row, i % 2 === 0 ? styles.backWhite : styles.backWhite]}>
                <View style={[styles.bordaPrimeiraCelula, styles.w12, styles.centralized]}>
                  <Text>{g.ncm}</Text>
                </View>
                <View style={[styles.bordaCelula, styles.w48, { paddingLeft: 2 }]}>
                  <Text>{g.descricao.trim()}</Text>
                </View>
                <View style={[styles.bordaCelula, styles.w20, styles.centralized]}>
                  <Text>{formatarValor(g.quantidade)}</Text>
                </View>
                <View style={[styles.bordaCelula, styles.w20, styles.value]}>
                  <Text>{formatarValor(g.valor)}</Text>
                </View>
              </View>
            );
          })}
        </View>
        <Text
          fixed
          style={{
            position: "absolute",
            bottom: 10,
            left: 0,
            right: 0,
            textAlign: "center",
            fontSize: 10,
            fontFamily: "Times-Roman",
          }}
          render={({ pageNumber, totalPages }) => `Página ${pageNumber} de ${totalPages}`}
        />
      </Page>
    </Document>
  );
}
