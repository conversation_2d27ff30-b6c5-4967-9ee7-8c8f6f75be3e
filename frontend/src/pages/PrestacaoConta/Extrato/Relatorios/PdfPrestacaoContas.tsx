import { environment } from "@/env";
import { Document, Image, Page, Text, View } from "@react-pdf/renderer";
import QRCode from "qrcode";
import { styles } from "./PdfPrestacaoContas.estilos";

import brasao from "@/assets/brasaodearmasSC.jpg";
import {
  aplicarMascaraCNPJ,
  aplicarMascaraCartao,
  formatarData,
  formatarDataHora,
  formatarValor,
} from "@/helpers/formatacao";
import type { Credito, Movimentacao, UsuarioAutenticado } from "cpesc-shared";
import type { ReactNode } from "react";

export interface PdfPrestacaoContasProps {
  credito: Credito;
  movimentacoes: Movimentacao[];
  totalGastos: number;
  usuario: UsuarioAutenticado;
  orgao: string;
  processo: string;
  ano: string;
}

export async function PdfPrestacaoContas(props: PdfPrestacaoContasProps) {
  const qrCodeBase64 = await gerarQRCodeBase64(`${environment.frontend}/extrato/${props.credito.id}`);
  const totalCreditos = props.credito.valorCredito;
  const totalGastos = Math.round(props.totalGastos * 100) / 100;

  const nomeUsuario = props.usuario.nome;
  const dataImpressao = formatarDataHora(new Date());
  const ipUsuario = props.usuario.ipAddress ?? "Desconhecido";

  return (
    <Document>
      <Page size="A4" style={styles.pagina}>
        <View fixed>
          <View style={styles.secao}>
            <Image src={brasao} style={styles.brasao} />
            <View>
              <Text style={styles.titulo}>Estado de Santa Catarina</Text>
              <Text style={styles.subTitulo}>
                Demonstrativo para Prestação de Contas {"\n"}
                Cartão de Pagamentos do Estado de Santa Catarina - CPESC
              </Text>
            </View>
            <Image src={qrCodeBase64} style={styles.brasao} />
          </View>
        </View>
        <View style={styles.table}>
          <View style={styles.row}>
            <Text style={[styles.cell, styles.w100]}>
              <Campo rotulo="Processo SGPE">{props.orgao + props.processo + "/" + props.ano}</Campo>
            </Text>
          </View>
        </View>
        <View style={styles.table}>
          <View style={styles.row}>
            <Text style={[styles.cell, styles.w70]}>
              <Campo rotulo="Unidade Concedente">
                {props.credito.cartao.portadorUnidadeAdministrativa.unidadeAdministrativa.unidadeGestora.nome}
              </Campo>
            </Text>
            <Text style={[styles.cell, styles.w30]}>
              <Campo rotulo="CNPJ">
                {aplicarMascaraCNPJ(
                  props.credito.cartao.portadorUnidadeAdministrativa.unidadeAdministrativa.unidadeGestora.cnpj,
                )}
              </Campo>
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={[styles.cell, styles.w70]}>
              <Campo rotulo="Ordenador">
                {props.credito.cartao.portadorUnidadeAdministrativa.unidadeAdministrativa.unidadeGestora.nomeOrdenador}
              </Campo>
            </Text>
            <Text style={[styles.cell, styles.w30]}>
              <Campo rotulo="Conta Corrente">
                {props.credito.cartao.portadorUnidadeAdministrativa.unidadeAdministrativa.unidadeGestora.contaBanco
                  .conta +
                  "-" +
                  props.credito.cartao.portadorUnidadeAdministrativa.unidadeAdministrativa.unidadeGestora.contaBanco
                    .digitoConta}
              </Campo>
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={[styles.cell, styles.w70]}>
              <Campo rotulo="Unidade Administrativa">
                {props.credito.cartao.portadorUnidadeAdministrativa.unidadeAdministrativa.nome}
              </Campo>
            </Text>
            <Text style={[styles.cell, styles.w30]}>
              <Campo rotulo="Município">
                {props.credito.cartao.portadorUnidadeAdministrativa.unidadeAdministrativa.municipio.nomeMunicipio}
              </Campo>
            </Text>
          </View>
        </View>
        <View style={styles.table}>
          <View style={styles.row}>
            <Text style={[styles.cell, styles.w100]}>
              <Campo rotulo="Suprido">{props.credito.cartao.portadorUnidadeAdministrativa.portador.nome}</Campo>
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={[styles.cell, styles.w30]}>
              <Campo rotulo="Matrícula">{props.credito.cartao.portadorUnidadeAdministrativa.matricula}</Campo>
            </Text>
            <Text style={[styles.cell, styles.w30]}>
              <Campo rotulo="CPF">{props.credito.cartao.portadorUnidadeAdministrativa.portador.cpfOfuscado}</Campo>
            </Text>
            <Text style={[styles.cell, styles.w30]}>
              <Campo rotulo="Cartão">{aplicarMascaraCartao(props.credito.cartao.numero, false)}</Campo>
            </Text>
          </View>
        </View>
        <Text style={styles.cabecalhoSecao}>Créditos</Text>
        <View style={styles.table}>
          <View style={[styles.row, styles.headerRow]}>
            <View style={[styles.bordaPrimeiroCabecalho, styles.w15]}>
              <Text>Data </Text>
            </View>
            <View style={[styles.bordaCabecalho, styles.w15, styles.centralized]}>
              <Text>Data Limite</Text>
            </View>
            <View style={[styles.bordaCabecalho, styles.w15, styles.centralized]}>
              <Text>NE</Text>
            </View>
            <View style={[styles.bordaCabecalho, styles.w15, styles.centralized]}>
              <Text>OB</Text>
            </View>
            <View style={[styles.bordaCabecalho, styles.w15, styles.centralized]}>
              <Text>NL</Text>
            </View>
            <View style={[styles.bordaCabecalho, styles.w15, styles.centralized]}>
              <Text>PP</Text>
            </View>
            <View style={[styles.bordaCabecalho, styles.w12, styles.centralized]}>
              <Text>Fonte</Text>
            </View>
            <View style={[styles.bordaCabecalho, styles.w12]}>
              <Text>Valor(R$)</Text>
            </View>
          </View>
          <View key={0} style={[styles.row]}>
            <View style={[styles.bordaPrimeiraCelula, styles.w15, styles.centralized]}>
              <Text>{formatarData(props.credito.dataCredito.toString())}</Text>
            </View>
            <View style={[styles.bordaCelula, styles.w15, styles.centralized]}>
              <Text>{formatarData(props.credito.dataLimiteMovimentacao)}</Text>
            </View>
            <View style={[styles.bordaCelula, styles.w15, styles.centralized]}>
              <Text>{props.credito.notaEmpenho}</Text>
            </View>
            <View style={[styles.bordaCelula, styles.w15, styles.centralized]}>
              <Text>{props.credito.ordemBancaria}</Text>
            </View>
            <View style={[styles.bordaCelula, styles.w15, styles.centralized]}>
              <Text>{props.credito.notaLancamento}</Text>
            </View>
            <View style={[styles.bordaCelula, styles.w15, styles.centralized]}>
              <Text>{props.credito.preparacaopagamento}</Text>
            </View>
            <View style={[styles.bordaCelula, styles.w12]}>
              <Text>{props.credito.fonteRecurso}</Text>
            </View>
            <View style={[styles.bordaCelula, styles.w12, styles.value]}>
              <Text>{formatarValor(props.credito.valorCredito)}</Text>
            </View>
          </View>
        </View>
        <Text style={styles.cabecalhoSecao}>Aplicação dos Recursos</Text>
        <View style={styles.table}>
          <View style={[styles.row, styles.headerRow]}>
            <View style={[styles.bordaPrimeiroCabecalho, styles.w12]}>
              <Text>Data</Text>
            </View>
            <View style={[styles.bordaCabecalho, styles.w48]}>
              <Text>Estabelecimento</Text>
            </View>
            <View style={[styles.bordaCabecalho, styles.w20]}>
              <Text>Município/UF</Text>
            </View>
            <View style={[styles.bordaCabecalho, styles.w20]}>
              <Text>Doc. Fiscal</Text>
            </View>
            <View style={[styles.bordaCabecalho, styles.w20]}>
              <Text>Valor</Text>
            </View>
          </View>
          {props.movimentacoes.map((g, i) => {
            return (
              <View key={i} style={[styles.row, i % 2 === 0 ? styles.backWhite : styles.backWhite]}>
                <View style={[styles.bordaPrimeiraCelula, styles.w12, styles.centralized]}>
                  <Text>{formatarData(g.dataTransacao)}</Text>
                </View>
                <View style={[styles.bordaCelula, styles.w48, { paddingLeft: 2 }]}>
                  <Text>
                    {g.nomeEstabelecimento}
                    {"/"}
                    {aplicarMascaraCNPJ(g.CNPJEstabelecimento)}
                  </Text>
                </View>
                <View style={[styles.bordaCelula, styles.w20, styles.centralized]}>
                  <Text>
                    {g.cidadeEstabelecimento}/{g.ufEstabelecimento}
                  </Text>
                </View>
                <View style={[styles.bordaCelula, styles.w20, styles.centralized]}>
                  <Text>
                    {g.notaFiscal?.numero}-{g.notaFiscal?.serie}
                  </Text>
                </View>
                <View style={[styles.bordaCelula, styles.w20, styles.value]}>
                  <Text>{formatarValor(g.valorTransacaoReal)}</Text>
                </View>
              </View>
            );
          })}
          <View wrap={false}>
            <View style={[styles.row, styles.footerRow]}>
              <View style={[styles.bordaPrimeiroCabecalho, styles.w80, styles.footerTotal]}>
                <Text>Total de Gastos</Text>
              </View>
              <View style={[styles.bordaCabecalho, styles.w20, styles.footerTotal]}>
                <Text>{formatarValor(totalGastos)}</Text>
              </View>
            </View>
            <View style={[styles.row, styles.footerRow]}>
              <View style={[styles.bordaPrimeiraCelula, styles.w80, styles.footerTotal]}>
                <Text>Saldo a Devolver</Text>
              </View>
              <View style={[styles.bordaCelula, styles.w20, styles.footerTotal]}>
                <Text>{formatarValor(totalCreditos - totalGastos)}</Text>
              </View>
            </View>
          </View>
        </View>
        <View style={{ height: "20px" }} />
        <View style={[styles.table, styles.tabelaAssinaturas]} wrap={false}>
          <View style={styles.row}>
            <Text style={styles.w10} />
            <Text style={[styles.row, styles.w80]}>
              DECLARO para os devidos fins e a quem possa interessar que atendo aos requisitos necessários para a
              guarda, a utilização e a prestação de contas do adiantamento identificado como Cartão CPESC , conforme
              estabelece o Decreto nº 1. 322 de 05 de outubro de 2017, alterado pelo Decreto nº 1.844 de 04 de abril de
              2022 e a Instrução Normativa TC nº 14 de 2012, e estou ciente das minhas responsabilidades e sanções
              cabíveis.
            </Text>
          </View>
          <View style={styles.row}>
            <View style={styles.w25} />
            <View style={[styles.cell, styles.w50, styles.assinatura, styles.centralized]}>
              <Text style={styles.underline} />
              <Text>
                Assinatura do Suprido{"\n"}({props.credito.cartao.portadorUnidadeAdministrativa.portador.nome})
              </Text>
            </View>
          </View>
          <View style={styles.row}>
            <View style={styles.w10} />
            <View style={[styles.cell, styles.w40, styles.assinatura]}>
              <Text style={styles.underline} />
              <Text>
                Assinatura Analista prestação de contas{"\n"}
                (Documento Assinado Digitalmente)
              </Text>
            </View>
            <View style={[styles.cell, styles.w40, styles.assinatura]}>
              <Text style={styles.underline} />
              <Text>
                Assinatura Controle Interno{"\n"}
                (Documento Assinado Digitalmente){"\n"}
              </Text>
            </View>
          </View>
          <View style={styles.row}>
            <View style={styles.w20} />
            <View style={[styles.cell, styles.w80, styles.localData]}>
              <Text>
                {props.credito.cartao.portadorUnidadeAdministrativa.unidadeAdministrativa.municipio.nomeMunicipio},{" "}
                {formatarDataHora(new Date())}
              </Text>
            </View>
          </View>
        </View>
        <Text
          fixed
          style={styles.footerPageIp}
          render={() => `Impresso em ${dataImpressao} por ${nomeUsuario} do IP ${ipUsuario}`}
        />
        <Text
          fixed
          style={styles.footerPage}
          render={({ pageNumber, totalPages }) => `Página ${pageNumber} de ${totalPages}`}
        />
      </Page>
    </Document>
  );
}

function Campo(props: { rotulo: string; children: ReactNode }) {
  return (
    <>
      <Text style={{ fontFamily: "Helvetica-Bold" }}>{props.rotulo}: </Text>
      <Text>{props.children}</Text>
    </>
  );
}

async function gerarQRCodeBase64(url: string): Promise<string> {
  try {
    const base64 = await QRCode.toDataURL(url);
    return base64;
  } catch (err) {
    console.error("Erro ao gerar QRCode", err);
    return "";
  }
}
