import { StyleSheet } from "@react-pdf/renderer";
export const styles = StyleSheet.create({
  hSplit: {
    display: "flex",
    flexDirection: "row",
  },
  vSplit: {
    display: "flex",
    flexDirection: "column",
  },
  negrito: {
    fontFamily: "Times-Bold",
    fontWeight: "bold",
  },
  table: {
    display: "flex",
    flexDirection: "column",
    width: "100%",
    marginBottom: 4,
  },
  row: {
    display: "flex",
    flexDirection: "row",
    width: "100%",
    textAlign: "justify",
  },
  headerRow: {
    fontFamily: "Helvetica-Bold",
    textAlign: "center",
  },
  footerRow: {
    fontFamily: "Helvetica-Bold",
    textAlign: "center",
  },
  footerTotal: {
    textAlign: "right",
    fontSize: "10",
    fontFamily: "Helvetica-Bold",
    paddingRight: 2,
  },
  footerPage: {
    position: "absolute",
    bottom: 10,
    left: 0,
    right: 0,
    textAlign: "center",
    fontSize: 10,
    fontFamily: "Times-Roman",
  },
  footerPageIp: {
    position: "absolute",
    bottom: 30,
    left: 0,
    right: 0,
    textAlign: "center",
    fontSize: 10,
    fontFamily: "Times-Roman",
  },
  cell: {
    padding: 0,
    marginBottom: 0.7,
    marginTop: 1,
    marginLeft: 1,
    marginRight: 1,
    height: "16px",
  },
  backGray: {
    backgroundColor: "gray",
  },
  backWhite: {
    backgroundColor: "white",
  },
  w8: { width: "8%" },
  w10: { width: "10%" },
  w12: { width: "12%" },
  w15: { width: "15%" },
  w20: { width: "20%" },
  w25: { width: "25%" },
  w30: { width: "30%" },
  w33: { width: "33%" },
  w34: { width: "34%" },
  w40: { width: "40%" },
  w48: { width: "48%" },
  w50: { width: "50%" },
  w60: { width: "60%" },
  w70: { width: "70%" },
  w80: { width: "80%" },
  w100: { width: "100%" },
  centralized: {
    textAlign: "center",
    alignContent: "center",
  },
  underline: {
    borderBottom: "1px",
    borderBottomColor: "#3e3e3e",
    borderBottomStyle: "solid",
  },
  value: {
    textAlign: "right",
  },
  pagina: {
    fontSize: 10,
    paddingTop: 60, // 3 cm
    paddingLeft: 60, // 3 cm
    paddingRight: 40, // 2 cm
    paddingBottom: 40,
  },
  secao: {
    display: "flex",
    flexDirection: "row",
    paddingBottom: "20",
  },
  brasao: {
    marginTop: 2,
    width: 60,
  },
  titulo: {
    fontSize: 18,
    marginTop: 5,
    marginLeft: 25,
  },
  subTitulo: {
    fontSize: 12,
    marginTop: 4,
    marginLeft: 25,
  },
  cabecalhoSecao: {
    fontSize: 14,
    marginTop: 4,
    marginBottom: 2,
  },
  bordaPrimeiroCabecalho: {
    padding: 3,
    borderLeft: "1px",
    borderTop: "1px",
    borderRight: "1px",
    borderBottom: "1px",
  },
  bordaCabecalho: {
    padding: 3,
    borderLeft: "0px",
    borderTop: "1px",
    borderRight: "1px",
    borderBottom: "1px",
  },
  bordaPrimeiraCelula: {
    fontSize: 8,
    padding: 3,
    borderLeft: "1px",
    borderTop: "0px",
    borderRight: "1px",
    borderBottom: "1px",
  },
  bordaCelula: {
    fontSize: 8,
    padding: 3,
    borderLeft: "0px",
    borderTop: "0px",
    borderRight: "1px",
    borderBottom: "1px",
  },
  tabelaAssinaturas: {
    paddingTop: 16,
    margin: 0,
    padding: 0,
  },
  assinatura: {
    fontFamily: "Helvetica-Bold",
    paddingTop: 48,
    textAlign: "center",
  },
  localData: {
    fontFamily: "Helvetica-Bold",
    paddingTop: 48,
    textAlign: "right",
  },
});
