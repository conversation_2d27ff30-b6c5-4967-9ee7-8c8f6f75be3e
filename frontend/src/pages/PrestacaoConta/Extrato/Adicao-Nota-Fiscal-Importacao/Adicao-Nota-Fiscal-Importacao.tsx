import ContainerQuery from "@/components/ContainerQuery";
import SpinnerCpesc from "@/components/SpinnerCpesc";
import TextoErro from "@/components/TextoErro";
import { chamarApi } from "@/helpers/api";
import { opcoesNotaFiscal } from "@/helpers/constantes";
import { aplicarMascaraCNPJ, formatarData, formatarMoeda } from "@/helpers/formatacao";
import telaPadrao from "@/tela-padrao.module.scss";
import { useQuery } from "@tanstack/react-query";
import { type NotaFiscal, type NotaFiscalSigef, TipoNotaFiscal } from "cpesc-shared";
import { endpoints } from "cpesc-shared/out/endpoints/main";
import type { PayloadCriarNFImportacao } from "cpesc-shared/out/endpoints/nota-fiscal";
import { Button } from "primereact/button";
import { Column } from "primereact/column";
import { DataTable } from "primereact/datatable";
import { Dialog } from "primereact/dialog";
import { Divider } from "primereact/divider";
import { InputMask } from "primereact/inputmask";
import { InputText } from "primereact/inputtext";
import { Message } from "primereact/message";
import { useState } from "react";
import { type OperacaoTabelaGastos, TipoOperacaoTabelaGastos } from "../Tabelas/TabelaGastos";
import estilo from "./Adicao-Nota-Fiscal-Importacao.module.scss";

interface AdicaoNotaFiscalImportacaoProps {
  movimentacaoId: number;
  email: string;
  chave?: string;
  operacao: OperacaoTabelaGastos | null;
  onSelecionar: (notaFiscal: PayloadCriarNFImportacao) => void;
  onFechar: () => void;
}

export default function AdicaoNotaFiscalImportacao(props: AdicaoNotaFiscalImportacaoProps) {
  const [buscaPermitida, setBuscaPermitida] = useState<boolean>(false);
  const [parametro, setParametro] = useState<string | undefined>(undefined);
  const [cnpj, setCnpj] = useState<string | undefined>(undefined);
  const [numero, setNumero] = useState<string | undefined>(undefined);
  const [serie, setSerie] = useState<string | undefined>(undefined);
  const [chave, setChave] = useState<string | undefined>(props.chave);

  const { data, isPending, error } = useQuery({
    queryKey: ["/nota-fiscal/buscar-externo/", parametro],
    queryFn: () => chamarApi(endpoints.buscarNotasFiscaisExternas, { parametro: parametro ?? "" }),
    enabled: buscaPermitida,
  });

  // Botão para selecionar a nota
  const selecionarNotaTemplate = (rowData: NotaFiscal) => {
    return <Button label="Selecionar" onClick={() => selecionarNota(rowData)} className={telaPadrao.botaoPrincipal} />;
  };

  // Salva a nota fiscal no banco de dados e passa resultado para o componente pai
  const selecionarNota = (nota: NotaFiscal) => {
    if (
      nota.chave == null ||
      nota.cnpj === null ||
      nota.codigosigef == null ||
      nota.dataemissao === null ||
      nota.numero === null ||
      nota.serie === null ||
      nota.valor === null
    ) {
      throw Error("Dados da nota fiscal incompletos.");
    }

    const payloadCriacao: PayloadCriarNFImportacao = {
      chave: nota.chave,
      cnpj: nota.cnpj,
      codigosigef: nota.codigosigef,
      dataemissao: nota.dataemissao instanceof Date ? nota.dataemissao.toISOString() : nota.dataemissao,
      movimentacao_id: props.movimentacaoId,
      numero: nota.numero,
      serie: nota.serie,
      tiponotafiscal_id: TipoNotaFiscal.notaFiscalEletronicaImportacao,
      valor: nota.valor,
    };

    props.onSelecionar(payloadCriacao);
    onHideDialog();
  };

  // Botão para detalhar a nota
  const detalharNotaTemplate = (rowData: NotaFiscal) => {
    if (rowData.chave) {
      return (
        <Button
          label="Mais detalhes"
          onClick={() => detalharNota(rowData.chave ?? "")}
          className={telaPadrao.botaoSecundario}
        />
      );
    }

    return null;
  };

  //Formatação do CNPJ
  const cnpjTemplate = (rowData: NotaFiscal) => {
    return aplicarMascaraCNPJ(rowData.cnpj ?? "");
  };

  //Formatação da data
  const dataTemplate = (rowData: NotaFiscal) => {
    return formatarData(rowData.dataemissao ?? "");
  };

  //Formatação do valor
  const valorTemplate = (rowData: NotaFiscal) => {
    return formatarMoeda(rowData.valor);
  };

  //Abrir link para detalhar nota no SAT
  const detalharNota = (chave: string) => {
    if (chave.length == 44) {
      return window.open(
        "https://sat.sef.sc.gov.br/tax.NET/Sat.NFe.Web/Consultas/ConsultaPublicaNFe.aspx?chaveacesso=" + chave,
        "_blank",
      );
    }
    return null;
  };

  //Validação dos campos e chamada da API
  function botaoBuscarNota(): void {
    if ((numero && serie && cnpj) || chave) {
      setBuscaPermitida(true);
      if (chave) {
        setParametro(chave);
      } else if (cnpj && numero && serie) {
        setParametro(cnpj.replace(/[^A-Z0-9]/g, "") + "-" + numero + "-" + serie);
      }
    } else {
      setBuscaPermitida(false);
    }
  }

  //Tabela com os dados da nota fiscal
  function TabelaBuscarNota(props: { dados: NotaFiscalSigef[] | undefined }) {
    if (props.dados) {
      const listanotas = props.dados;
      return (
        <>
          {listanotas.length > 0 && (
            <div>
              <div className={telaPadrao.painel}>
                <Message
                  severity="success"
                  text="Nota fiscal encontrada: confira e selecione para adicionar ao Extrato de Prestação de Contas."
                  className={estilo.mensagemInferior}
                />
                <DataTable
                  value={listanotas}
                  resizableColumns
                  tableStyle={{ fontSize: "13px" }}
                  className={estilo.tabela}
                  responsiveLayout="stack"
                  breakpoint="768px"
                >
                  <Column body={cnpjTemplate} field="cnpj" header="CNPJ" />
                  <Column field="numeroserie" header="Número-série" />
                  <Column body={dataTemplate} field="dataemissao" header="Data" />
                  <Column body={valorTemplate} field="valor" header="Valor" />
                  <Column body={detalharNotaTemplate} field="chave" />
                  <Column body={selecionarNotaTemplate} />
                </DataTable>
              </div>
            </div>
          )}
          {listanotas.length == 0 && buscaPermitida && (
            <Message
              severity="warn"
              text="Atenção: Nota fiscal não encontrada. Verifique a busca realizada e tente novamente."
              className={estilo.mensagemInferior}
            />
          )}
        </>
      );
    }

    return null;
  }

  function onHideDialog() {
    // Reseta todos os estados para seus valores iniciais
    props.onFechar();
    setBuscaPermitida(false);
    setParametro(undefined);
    setCnpj(undefined);
    setNumero(undefined);
    setSerie(undefined);
    setChave(undefined);
  }

  return (
    <>
      <div className={telaPadrao.container}>
        <Dialog
          visible={
            props.operacao?.tipoOperacao === TipoOperacaoTabelaGastos.AdicionarImportacao ||
            props.operacao?.tipoOperacao === TipoOperacaoTabelaGastos.EditarImportacao
          }
          modal
          style={{ width: "50vw" }}
          breakpoints={{ "1534px": "60vw", "1280px": "70vw", "1096px": "80vw", "960px": "90vw", "854px": "100vw" }}
          contentStyle={{ background: "hsl(45, 80%, 98%)" }}
          onHide={onHideDialog}
          closeOnEscape={true}
          header={opcoesNotaFiscal
            .filter(opcao => opcao.codigo === TipoNotaFiscal.notaFiscalEletronicaImportacao)
            .map(opcao => opcao.descricao)}
          headerStyle={{ background: "hsl(45, 80%, 98%)" }}
        >
          <div className={estilo.formContainer}>
            <div className={estilo.formSection}>
              <div className={estilo.formGroup}>
                <label htmlFor="cnpj">CNPJ do Estabelecimento</label>
                <InputMask
                  id="cnpj"
                  mask="**.***.***/****-99"
                  keyfilter={/[A-Z0-9]/}
                  title="Apenas números e letras maiúsculas são permitidos."
                  className={estilo.input}
                  value={cnpj}
                  onChange={e => setCnpj(e.target.value ?? "")}
                />
              </div>
              <div className={estilo.formGroup}>
                <label htmlFor="numero">Número da Nota</label>
                <InputText
                  id="numero"
                  maxLength={10}
                  keyfilter="int"
                  className={estilo.input}
                  value={numero}
                  onChange={e => setNumero(e.target.value)}
                />
              </div>
              <div className={estilo.formGroup}>
                <label htmlFor="serie">Série da Nota</label>
                <InputText
                  id="serie"
                  maxLength={10}
                  keyfilter="int"
                  className={estilo.input}
                  value={serie}
                  onChange={e => setSerie(e.target.value)}
                />
              </div>
            </div>
            <div className={estilo.colunaCampos}>
              <Divider layout="vertical" className="hidden md:flex">
                <span>ou</span>
              </Divider>
              <Divider layout="horizontal" className="flex md:hidden" align="center">
                <span>ou</span>
              </Divider>
            </div>

            <div className={estilo.formSection}>
              <div className={estilo.formGroup}>
                <label htmlFor="chave">Chave de acesso</label>
                <InputText
                  id="chave"
                  maxLength={44}
                  keyfilter="int"
                  className={estilo.input}
                  value={chave ?? ""}
                  onChange={e => setChave(e.target.value)}
                />
              </div>
            </div>
          </div>

          <div className={estilo.painelBotao}>
            <Button className={telaPadrao.botaoPrincipal} label="Buscar Nota" onClick={botaoBuscarNota} />
          </div>

          {buscaPermitida && (
            <ContainerQuery
              carregando={isPending}
              fallbackCarregamento={
                <div className="text-center">
                  <SpinnerCpesc />
                </div>
              }
              erro={error}
              fallbackErro={<TextoErro mensagem="Falha ao obter dados" />}
            >
              <TabelaBuscarNota dados={data} />
            </ContainerQuery>
          )}
          {!buscaPermitida && (
            <Message
              severity="info"
              text="Preencha CNPJ, Número e Série ou somente a Chave para buscar uma nota fiscal eletrônica. "
              className={estilo.mensagemInferior}
            />
          )}
          <div className={telaPadrao.espaco} />
        </Dialog>
      </div>
    </>
  );
}
