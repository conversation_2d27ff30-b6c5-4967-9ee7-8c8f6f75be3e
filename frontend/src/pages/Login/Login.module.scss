@use "@/variables.scss";

.container {
  align-items: center;
  display: flex;
  flex-direction: column;
  height: 100vh;
  justify-content: center;
}

.icone {
  height: 100px;
  width: auto;
  display: block;
  margin: 1.5rem auto;
}

.cardLogin {
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  font-size: 1rem;
  margin-bottom: 2rem;
  padding: 2rem;
  width: 34rem;
}

.formulario {
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.alternativaLogin {
  align-items: center;
  color: #666;
  display: flex;
  margin-block: 1rem;
}

.divisor {
  color: #ccc;
  flex: 1;
  height: 1px;
}

.label {
  margin-bottom: 0.5rem;
  font-weight: bold;
}

.input {
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  font-size: 1.25rem;
  margin-bottom: 1rem;
  padding: 0.5rem;
}

.botaoContainer {
  display: flex;
  gap: 1rem;
  margin: 1rem;
}

.informacaoEmail {
  width: 60%;
  margin: 2rem auto;
  text-align: center;

  @media (max-width: 768px) {
    width: 80%;
  }
}
.esqueciEmailLink {
  border-radius: 0.25rem;
  color: #6c757d;
  display: flex;
  font-size: 0.85rem;
  gap: 0.5rem;

  &:hover {
    text-decoration: underline;
  }

  &:focus {
    outline: 1px solid #6c757d;
    outline-offset: 0.5rem;
  }
}

.tabelaRecuperarEmail {
  display: table;

  .campo {
    display: table-row;

    & > * {
      display: table-cell;
      padding: 0.5rem;
    }
  }
  @media (max-width: 768px) {
    display: block;

    .campo {
      display: block;
      margin: 1rem;
    }
  }
}
