import SpinnerCpesc from "@/components/SpinnerCpesc";
import { chamarApi } from "@/helpers/api";
import { useToast } from "@/hooks/useToast";
import telaPadrao from "@/tela-padrao.module.scss";
import { useMutation } from "@tanstack/react-query";
import classNames from "classnames";
import { endpoints } from "cpesc-shared/out/endpoints/main";
import { Button } from "primereact/button";
import { InputText } from "primereact/inputtext";
import React, { useState } from "react";
import estilos from "./Login.module.scss";

interface FormEmailProps {
  onEnviar: (email: string) => void;
  onEsqueciEmail: () => void;
  onLoginSau: () => void;
}

export function FormEmail(props: FormEmailProps) {
  const [email, setEmail] = useState(() => sessionStorage.getItem("cpesc-email") ?? "");
  const [mensagem, setMensagem] = useState("Enviaremos um código para seu e-mail, necessário para realizar o login.");
  const toast = useToast();
  const mutationOtp = useMutation({
    mutationFn: (email: string) => chamarApi(endpoints.otp, {}, { email }),
  });

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!emailRegex.test(email)) {
      toast.show({
        severity: "error",
        summary: `Por favor, insira um email válido. O email ${email} não atende os requisitos mínimos.`,
        life: 5000,
      });
      return;
    }

    mutationOtp.mutate(email, {
      onSuccess: () => {
        sessionStorage.setItem("cpesc-email", email);
        toast.show({
          severity: "success",
          summary: `Um novo código foi enviado para o e-mail [${email}]... \nVerifique sua caixa de entrada ou spam.`,
          life: 5000,
        });
        props.onEnviar(email);
      },
      onError: error => {
        setMensagem(error.message);
        toast.show({
          severity: "error",
          summary: `Erro ao enviar email: ${error.message}`,
          life: 10000,
        });
        mutationOtp.reset();
      },
    });
  };

  return (
    <section className={estilos.cardLogin}>
      <form onSubmit={handleSubmit} className={estilos.formulario}>
        <span className={estilos.label}>Informe seu e-mail:</span>

        <InputText
          className={estilos.input}
          type="email"
          name="email"
          value={email}
          onChange={e => setEmail(e.target.value)}
          placeholder="<EMAIL>"
          disabled={mutationOtp.isPending}
          required
          autoFocus
        />

        {mutationOtp.isPending ? (
          <SpinnerCpesc
            style={{
              width: "40px",
              height: "40px",
            }}
          />
        ) : (
          <Button className={telaPadrao.botaoPrincipal} label="Login com e-mail" type="submit" />
        )}

        <div className={estilos.informacaoEmail}>{mensagem}</div>

        <Button className={estilos.esqueciEmailLink} onClick={props.onEsqueciEmail} type="button">
          <i className="pi pi-external-link" /> Esqueci meu email...
        </Button>
      </form>

      <div className={estilos.alternativaLogin}>
        <hr className={estilos.divisor} />
        <span className="mx-2">ou</span>
        <hr className={estilos.divisor} />
      </div>

      <div className="text-center">
        <Button
          className={classNames(telaPadrao.botaoSecundario, "m-0")}
          label="Login via SAU"
          type="button"
          onClick={props.onLoginSau}
        />
      </div>
    </section>
  );
}
