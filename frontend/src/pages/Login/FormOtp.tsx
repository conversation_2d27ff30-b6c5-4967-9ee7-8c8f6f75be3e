import SpinnerCpesc from "@/components/SpinnerCpesc";
import { useOptionalAuth } from "@/contexts/auth/useAuth";
import telaPadrao from "@/tela-padrao.module.scss";
import classNames from "classnames";
import type { PayloadLoginOtp } from "cpesc-shared";
import { Button } from "primereact/button";
import { InputOtp } from "primereact/inputotp";
import React, { useState } from "react";
import estilos from "./Login.module.scss";

export function FormOtp(props: { email: string; onVoltar: () => void }) {
  const [codigoOtp, setCodigoOtp] = useState("");
  const auth = useOptionalAuth();

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    const payload: PayloadLoginOtp = { tipo: "otp", email: props.email, codigo: codigoOtp };
    auth.login(payload);
  };

  return (
    <section className={estilos.cardLogin}>
      <form onSubmit={handleSubmit} className={estilos.formulario}>
        <span className={estilos.label}>Informe o código OTP:</span>

        <InputOtp
          className={estilos.input}
          value={codigoOtp}
          onChange={e => setCodigoOtp(e.value?.toString() ?? "")}
          integerOnly
          length={6}
          disabled={auth.isLoginPending}
          required
          autoFocus
        />

        {auth.isLoginPending ? (
          <SpinnerCpesc
            style={{
              width: "40px",
              height: "40px",
              marginTop: "1rem",
            }}
          />
        ) : (
          <div className={estilos.botaoContainer}>
            <Button
              className={classNames(telaPadrao.botaoSecundario)}
              label="Voltar"
              type="button"
              onClick={props.onVoltar}
            />

            <Button
              className={telaPadrao.botaoPrincipal}
              label="Confirmar código"
              type="submit"
              disabled={auth.isLoginPending}
            />
          </div>
        )}
      </form>
    </section>
  );
}
