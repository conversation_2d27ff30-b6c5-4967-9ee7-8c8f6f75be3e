import { chamarApi } from "@/helpers/api";
import { useMutation } from "@tanstack/react-query";
import type { NotaFiscal } from "cpesc-shared";
import { endpoints } from "cpesc-shared/out/endpoints/main";

export function useMutationAlterarNota() {
  return useMutation({
    mutationFn: (nota: NotaFiscal) => chamarApi(endpoints.editarNotaFiscal, { id: nota.id.toString() }, nota),
    onError: error => {
      console.error(error);
    },
  });
}
