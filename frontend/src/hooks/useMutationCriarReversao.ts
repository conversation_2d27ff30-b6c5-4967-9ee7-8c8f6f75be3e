import { chamarApi } from "@/helpers/api";
import { useMutation } from "@tanstack/react-query";
import { endpoints } from "cpesc-shared/out/endpoints/main";
import type { PayloadCriarReversaoMovimentacao } from "cpesc-shared/out/endpoints/reversao-movimentacao";

export function useMutationCriarReversao() {
  return useMutation({
    mutationFn: (reversao: PayloadCriarReversaoMovimentacao) =>
      chamarApi(endpoints.criarReversaoMovimentacao, {}, reversao),
    onError: error => {
      console.error(error);
    },
  });
}
