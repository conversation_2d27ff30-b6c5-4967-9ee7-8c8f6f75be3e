import { chamarApi } from "@/helpers/api";
import { useMutation } from "@tanstack/react-query";
import { endpoints } from "cpesc-shared/out/endpoints/main";
import type { PayloadCriarNF } from "cpesc-shared/out/endpoints/nota-fiscal";

export function useMutationCriarNota() {
  return useMutation({
    mutationFn: (nota: PayloadCriarNF) => chamarApi(endpoints.criarNotaFiscal, {}, nota),
    onError: error => {
      console.error(error);
    },
  });
}
