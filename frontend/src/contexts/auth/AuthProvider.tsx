import { environment } from "@/env";
import { chamarApi } from "@/helpers/api";
import { normalizeIp } from "@/helpers/ipUtils";
import { useToast } from "@/hooks/useToast";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type { PayloadLogin, RetornoCredenciais } from "cpesc-shared";
import { endpoints } from "cpesc-shared/out/endpoints/main";
import type { ReactNode } from "react";
import { Auth, type AuthData } from "./useAuth";

export interface AuthProviderProps {
  children: ReactNode;
}

const UM_MINUTO_MS = 60 * 1000;

export function AuthProvider(props: AuthProviderProps) {
  const toast = useToast();
  const queryClient = useQueryClient();

  const queryCredenciais = useQuery({
    queryKey: ["credenciais"],
    queryFn: ({ signal }) => chamarApi(endpoints.lerCredenciais, {}, undefined, { signal }),
    staleTime: 15 * UM_MINUTO_MS,
  });

  const mutationLogin = useMutation({
    mutationFn: async (payload: PayloadLogin) => {
      const json = await chamarApi(endpoints.login, {}, payload);
      const credenciais = json.credenciais;
      queryClient.setQueryData<RetornoCredenciais>(["credenciais"], { credenciais });
    },
  });

  const mutationLogout = useMutation({
    mutationFn: async () => {
      const tipoLogin = queryCredenciais.data?.credenciais?.tipoLogin;
      await queryClient.cancelQueries({ queryKey: ["credenciais"] });
      queryClient.clear();
      queryClient.setQueryData<RetornoCredenciais>(["credenciais"], { credenciais: null });
      await chamarApi(endpoints.logout, {}, undefined);

      if (tipoLogin === "sau") {
        location.replace(environment.sauUrlLogout + environment.frontend + "/login");
      } else {
        location.replace(environment.frontend + "/login");
      }

      return tipoLogin;
    },
    onError: error => {
      toast.show({
        severity: "error",
        summary: error.message,
        life: 10000,
      });
    },
  });

  const login = (payload: PayloadLogin) => {
    mutationLogin.mutate(payload);
  };

  const logout = () => {
    mutationLogout.mutate();
  };

  const isLoginPending = mutationLogin.isPending;
  const isLogoutPending = mutationLogout.isPending;
  // Utilizado isLoading ao invés de isPending para impedir que atualizações automáticas das
  // credenciais bloqueem a tela atual.
  const carregando = queryCredenciais.isLoading || isLoginPending || isLogoutPending;
  let authData: AuthData;

  if (carregando) {
    authData = {
      usuario: null,
      carregando: true,
      erro: null,
      ipAddress: null,
      isLoginPending,
      isLogoutPending,
    };
  } else if (queryCredenciais.isError) {
    authData = {
      usuario: null,
      carregando: false,
      erro: queryCredenciais.error,
      ipAddress: null,
      isLoginPending,
      isLogoutPending,
    };
  } else {
    const rawIpAddress = queryCredenciais.data?.credenciais?.ipAddress ?? null;
    const ipAddress = normalizeIp(rawIpAddress);
    authData = {
      usuario: queryCredenciais.data?.credenciais ?? null,
      carregando: false,
      erro: null,
      ipAddress,
      isLoginPending,
      isLogoutPending,
    };
  }

  return <Auth.Provider value={{ ...authData, login, logout }}>{props.children}</Auth.Provider>;
}
