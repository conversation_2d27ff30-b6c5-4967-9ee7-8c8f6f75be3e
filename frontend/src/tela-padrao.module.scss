@use "@/variables";

.container {
  --largura-container: 100%;
  --largura-container-disponivel: calc(var(--largura-container) - 2 * var(--padding-principal));
  padding: var(--padding-principal);
  width: var(--largura-container-disponivel);
  margin: auto;

  @media (min-width: variables.$limite-largura-tela-padrao) {
    --largura-container: #{variables.$limite-largura-tela-padrao};
  }
  @media (min-width: variables.$limite-largura-tela-grande) {
    --largura-container: #{variables.$limite-largura-tela-grande};
  }
  @media (max-width: 428px) {
    padding: var(--padding-principal) / 2;
  }
}

.tituloTela {
  display: flex;

  & > *:first-child {
    flex: 1;
  }

  @media (max-width: variables.$limite-largura-tela-mobile) {
    flex-direction: column;
    & > *:first-child {
      margin-bottom: 0.25em;
    }
  }
}

h1 {
  font-size: 2em;
}

h2 {
  font-size: 1.6em;
}

.link {
  color: var(--cor-link);
  text-decoration: underline;

  &:hover {
    color: var(--cor-link-hover);
  }
}

.espaco {
  padding-bottom: 2em;
}

.painel {
  background-color: var(--cor-verde-background);
  display: flex;
  flex-direction: column;
  width: 100%;
}

.p-datatable .p-datatable-tbody > tr > td {
  padding: 0.25rem 0.5rem;
}

.botaoPrincipal {
  border: 2px solid var(--cor-verde-escuro);
  background-color: var(--cor-verde-escuro);
  border-radius: var(--border-radius-padrao);
  color: var(--cor-fundo-padrao);
  display: inline-flex;
  align-items: center;
  text-align: center;
  position: relative;
  padding-block: 0.5rem;
  padding-inline: 1.25rem;
  margin: 1rem;
  font-size: 1rem;

  &:hover {
    border-color: var(--cor-verde-medio);
    background-color: var(--cor-verde-medio);
  }

  &:focus {
    outline: 2px solid var(--cor-verde-escuro);
    outline-offset: 2px;
  }

  &:disabled {
    border-color: #cccccc;
    background-color: #e0e0e0;
    color: #888888;
    cursor: not-allowed;
    opacity: 0.7;
  }
}

.botaoSecundario {
  border: 2px solid var(--cor-verde-escuro);
  border-radius: var(--border-radius-padrao);
  color: var(--cor-verde-escuro);
  display: inline-flex;
  align-items: center;
  text-align: center;
  position: relative;
  margin: 1rem 0.5rem 1rem 1rem;
  padding-block: 0.5rem;
  padding-inline: 1.25rem;
  font-size: 1rem;

  &:hover {
    border-color: var(--cor-verde-medio);
    color: var(--cor-verde-medio);
    background-color: var(--cor-verde-background);
  }

  &:focus {
    outline: 2px solid var(--cor-verde-escuro);
    outline-offset: 2px;
  }
}

.botaoTabela {
  border: 2px solid var(--cor-verde-escuro);
  background-color: var(--cor-verde-escuro);
  border-radius: var(--border-radius-padrao);
  display: inline-flex;
  color: var(--cor-fundo-padrao);
  align-items: center;
  text-align: left;
  position: relative;
  margin: 0;
  padding-block: 0.25rem;
  padding-inline: 1.25rem;
  font-size: 0.75rem;
  height: auto;
  line-height: 1;
  width: 60px;

  &:hover {
    border-color: var(--cor-verde-medio);
    background-color: var(--cor-verde-medio);
  }
}

.botaoInativo {
  background-color: var(--cor-inativo);
}
