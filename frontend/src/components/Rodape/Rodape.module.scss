@use "@/variables.scss";

footer {
  font-size: 0.8rem;
  bottom: 0;

  a {
    color: var(--cor-verde-escuro);
    text-decoration: none;
    background-color: transparent;
    &:hover {
      color: var(--cor-link);
      text-decoration: underline;
    }
  }

  .info {
    background: #252525;
    color: white;
  }

  .conteudo {
    --largura-maxima-rodape: 100%;
    display: flex;
    margin: auto;
    max-width: var(--largura-maxima-rodape);

    @media (min-width: variables.$limite-largura-tela-padrao) {
      --largura-maxima-rodape: #{variables.$limite-largura-tela-padrao};
    }
    @media (min-width: variables.$limite-largura-tela-grande) {
      --largura-maxima-rodape: #{variables.$limite-largura-tela-grande};
    }
  }

  .bloco {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: start;
    gap: 1rem;

    @media (max-width: 512px) {
      flex-direction: column;
    }
  }

  // .logo {
  //   max-width: 15%;
  // }

  .governo {
    //max-width: 20%;
    padding-top: 30px;
  }

  .endereco,
  .contato {
    //max-width: 35%;
    padding-top: 30px;
  }

  .link,
  .link:hover {
    color: var(--cor-link-light);
  }

  .espaco {
    padding-left: 5px;
  }

  .desenvolvimento {
    flex-direction: column;
    max-width: 130%;
  }
  .copyright {
    background: #161616;
    color: white;
    padding: 18px;
    align-items: center;

    .ico {
      width: 48px;
      height: 28px;
      margin-left: 3px;
      display: inline-block;
      background-image: url("@/assets/ciasc_favicon.png");
      background-position: 0 center;
      background-repeat: no-repeat;
      vertical-align: middle;
    }
  }
}
