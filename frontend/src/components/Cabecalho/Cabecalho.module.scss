@use "@/variables.scss";

.cabecalho {
  border-bottom: 2px solid var(--cor-texto-destacado);
  color: var(--cor-texto-destacado);
  & .conteudo {
    display: flex;
    justify-content: space-between;
  }
}

.conteudo {
  --largura-maxima-cabecalho: 100%;
  align-items: center;
  margin: auto;
  max-width: var(--largura-maxima-cabecalho);
  padding: 0 var(--padding-principal);

  @media (min-width: variables.$limite-largura-tela-padrao) {
    --largura-maxima-cabecalho: #{variables.$limite-largura-tela-padrao};
  }
  @media (min-width: variables.$limite-largura-tela-grande) {
    --largura-maxima-cabecalho: #{variables.$limite-largura-tela-grande};
  }
}

.titulo {
  text-decoration: none;
  color: var(--cor-texto-destacado);

  h1 {
    align-items: center;
    display: flex;
    font-size: 20px;
    gap: 1.5rem;
    @media (max-width: 512px) {
      & > span {
        display: none;
      }
    }
  }
}

/* barra do menu superior */
.menuBar {
  background-color: transparent;
  border: none;
}

/* itens do menu */
.menuBar ul li {
  padding: 0;
  border: 1pt transparent solid;
  /* z-index: 9999; */ //aparentemente nao eh mais necessario

  &[aria-expanded="true"] {
    background-color: var(--cor-menu-fundo);
    border: 1pt #eeeeee solid;
    border-radius: var(--border-radius-padrao);
  }
}

/* seta do menu */
.subMenu li[aria-expanded="true"] .menuContent .menuAction svg {
  transform: rotate(-90deg);
}

/* parte atras dos menu suspenso */
.subMenu {
  padding: 6px 10px;
  min-width: 300px;
  border-radius: var(--border-radius-padrao);
  box-shadow: 3px 2px 10px rgb(190, 190, 190, 0.7);
}
/*  parte interna dos menus */
.menuContent {
  background-color: transparent;
  padding: 10px;
  border: 1pt solid transparent;
  border-radius: var(--border-radius-padrao);
  gap: 1rem;

  a {
    border: 1pt var(--cor-menu-fundo) solid;
  }

  &:hover {
    text-decoration: none;
    background-color: var(--cor-menu-hover);
    border: 1pt #eeeeee solid;
  }
}

/* Labels dos menus */
.menuLabel {
  padding: 5px;
  text-decoration: none;
  color: var(--cor-texto-destacado) !important;

  &:hover,
  span:hover,
  a:hover {
    text-decoration: none;
  }
}

.link {
  align-items: center;
  border-radius: 0.25rem;
  display: flex;
  gap: 0.5rem;
  margin-left: clamp(8px, 1.5vw, 128px);

  &:hover {
    cursor: pointer;
    outline: 0.125rem solid #ddd;
    outline-offset: 0.25rem;
  }
}

.menuButton {
  display: none;
  @media screen and (max-width: 960px) {
    display: block;
  }
}

.logo {
  height: 32px;
  cursor: pointer;
}

.icone {
  font-size: 20px;
  padding-right: 10px;
}
