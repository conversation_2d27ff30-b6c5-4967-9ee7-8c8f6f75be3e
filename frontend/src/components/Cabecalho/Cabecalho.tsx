import { useOptionalAuth } from "@/contexts/auth/useAuth";
import { formatarNome } from "@/helpers/formatacao";
import { Perfil } from "cpesc-shared";
import { Menubar } from "primereact/menubar";
import type { MenuItem } from "primereact/menuitem";
import { Tooltip } from "primereact/tooltip";
import { Link } from "react-router-dom";
import logo from "../../assets/icone.png";
import styles from "./Cabecalho.module.scss";

export default function Cabecalho() {
  const auth = useOptionalAuth();

  const pt = {
    root: {
      className: styles.menuBar,
    },
    submenu: {
      className: styles.subMenu,
    },
    label: {
      className: styles.menuLabel,
    },
    content: {
      className: styles.menuContent,
    },
    action: {
      className: styles.menuAction,
    },
    button: {
      className: styles.menuButton,
    },
  };

  const perfilValor = auth.usuario?.perfil ?? Perfil.Portador;
  const menus: MenuItem[] = [];

  if ([Perfil.Gestor, Perfil.Consulta, Perfil.AdministradorCpesc, Perfil.AdministradorCiasc].includes(perfilValor)) {
    const itemsRelatorios = [];

    itemsRelatorios.push({
      icon: "pi pi-list",
      label: "Por Produto",
      url: "/prestacao-contas/relatorio-por-produto",
    });

    menus.push({
      label: "Relatórios",
      items: [...itemsRelatorios],
    });
  }

  if (perfilValor !== Perfil.Portador) {
    const itemsMovimentacoes = [];

    itemsMovimentacoes.push({
      icon: "pi pi-list-check",
      label: "Demonstrativo para Prestação de Contas",
      url: "/prestacao-contas/selecao-credito",
    });

    menus.push({
      label: "Movimentações",
      items: [...itemsMovimentacoes],
    });
  }

  return (
    <header>
      <div className={styles.cabecalho}>
        <div className={styles.conteudo}>
          <div className="flex align-itens-center">
            <Link to="/" className={styles.titulo}>
              <h1>
                <img src={logo} alt="CPESC 2.0" className={styles.logo} />
                <span>CPESC 2.0</span>
              </h1>
            </Link>
          </div>

          {/* TODO: combo da UG nesse local*/}

          {auth.usuario ? (
            <div>
              <div className="flex align-items-center gap-2">
                <Tooltip target=".usuario-tooltip" position="bottom" />
                <span className="usuario-tooltip" data-pr-tooltip={`${auth.usuario.email}`}>
                  <i className={`${styles.icone} pi pi-user`}></i>
                  {formatarNome(auth.usuario.nome)}
                </span>

                <span
                  className={styles.link}
                  onClick={() => {
                    auth.logout();
                    location.replace("/login");
                  }}
                >
                  <i className={`${styles.icone} pi pi-sign-out`}></i>
                  Sair
                </span>
              </div>
            </div>
          ) : (
            <span className={styles.link} onClick={() => window.location.replace("/login")}>
              <i className={`${styles.icone} pi pi-sign-out`}></i>
              Logar
            </span>
          )}
        </div>
      </div>

      <div className={styles.conteudo}>
        <Menubar pt={pt} model={menus} />
      </div>
    </header>
  );
}
