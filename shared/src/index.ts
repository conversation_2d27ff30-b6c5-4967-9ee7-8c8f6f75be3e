export type PayloadLogin = PayloadLoginSau | PayloadLoginOtp;

export interface PayloadLoginSau {
  tipo: "sau";
  rota: string;
  ticket: string;
}

export interface PayloadLoginOtp {
  tipo: "otp";
  email: string;
  codigo: string;
}

export type TipoLogin = PayloadLogin["tipo"];

export interface RetornoLogin {
  credenciais: UsuarioAutenticado;
}

export interface RetornoLogout {
  message: string;
}

export interface RetornoCredenciais {
  credenciais: UsuarioAutenticado | null;
}

export interface UsuarioAutenticado {
  id: number;
  perfil: Perfil;
  nome: string;
  email: string;
  ipAddress: string | null;
  tipoLogin: TipoLogin;
  rotaInicial: string;
}

export enum Perfil {
  Portador,
  GestorSed,
  Gestor,
  Consulta,
  AdministradorCpesc,
  AdministradorCiasc,
}

export enum HttpStatus {
  CONTINUE = 100,
  OK = 200,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  NOT_ACCEPTABLE = 406,
  INTERNAL_SERVER_ERROR = 500,
}
export interface CreditoCartao {
  id: number;
  dataCredito: Date;
  dataLimite: Date;
  preparacaopagamento: string;
  notaLancamento: string;
  valorCredito: number;
  prestacao: PrestacaoContas[];
  cartao: Cartao;
  subelemento: Subelemento;
  cdUnidadeGestora: string;
  unidadeGestora: UnidadeGestora;
  movimentacoes?: Movimentacao[];
  dataInicioMovimentacao: Date;
  dataLimiteMovimentacao: Date;
  temMaisLimites?: boolean;
}

export interface Credito extends CreditoCartao {
  notaEmpenho: string;
  ordemBancaria: string;
  fonteRecurso: string;
}

export interface UnidadeGestora {
  id: number;
  nome: string;
  codigo: number;
  descricao: string;
  cnpj: string;
  nomeOrdenador: string;
  contaBanco: ContaBanco;
}

export interface ContaBanco {
  conta: string;
  digitoConta: string;
}

export interface Portador {
  id: number;
  nome: string;
  nomeAbreviado: string;
  cpfOfuscado: string;
  email: string;
}

export interface Subelemento {
  id: number;
  subelemento: string;
  nome: string;
}

export interface PrestacaoContas {
  id: number;
  situacao: SituacaoPrestacaoConta;
  anoSgpe: string;
  orgaoSgpe: string;
  processoSgpe: string;
  valorGasto: number;
  limiteCartaoId: number;
  ip: string;
  ipCancelamento: string;
  motivoCancelamento: string;
  canceladoEm: Date;
  canceladoPor: string;
}

export enum SituacaoPrestacaoConta {
  Pendente = "P",
  Realizada = "R",
  Cancelada = "C",
}

export interface Cartao {
  id: number;
  numero: string;
  nuContaCartao: number;
  ativo: string;
  portadorUnidadeAdministrativa: PortadorUnidadeAdministrativa;
  movimentacoes: Movimentacao[];
}

export interface PortadorUnidadeAdministrativa {
  id: number;
  matricula: string;
  ativo: string;
  portador: Portador;
  unidadeAdministrativa: UnidadeAdministrativa;
}

export interface Movimentacao {
  id: number;
  numeroCartao: number;
  nomeEstabelecimento: string;
  CNPJEstabelecimento: string;
  cidadeEstabelecimento: string;
  ufEstabelecimento: string;
  valorTransacaoReal: number;
  dataTransacao: Date;
  horaTransacao: string;
  codigoTransacaoBB: number;
  descricaoTransacao: string;
  notaFiscal: NotaFiscal | null;
  reversao: ReversaoMovimentacao | null;
}

export interface UnidadeAdministrativa {
  id: number;
  nome: string;
  codigo: number;
  municipio: Municipio;
  unidadeGestora: UnidadeGestora;
}

export interface NotaFiscal {
  id: number;
  movimentacao_id?: number | null;
  tiponotafiscal_id: TipoNotaFiscal;
  numero: number | null;
  numeroserie: string;
  serie: number | null;
  cnpj: string | null;
  chave: string | null;
  dataemissao: string | Date | null;
  valor: number | null;
  desconto: number | null;
  cofins: number | null;
  iss: number | null;
  pis: number | null;
  inss: number | null;
  ir: number | null;
  notafiscalitens: NotaFiscalItem[] | null;
  codigosigef: number | null;
  criadopor?: string;
  atualizadopor?: string;
}

export interface ReversaoMovimentacao {
  id: number;
  reversaoId: number;
  movimentacaoId: number;
  revertidoEm: Date;
  criadoem?: Date;
  atualizadoem?: Date;
  criadopor?: string;
  atualizadopor?: string;
  limitecartao_id?: number;
}

export enum TipoNotaFiscal {
  notaFiscalEletronicaImportacao = 1,
  notaFiscalEletronicaManual = 2,
  notaServicoManual = 3,
  cupomFiscalManual = 4,
  reversao = 5,
}

export interface NotaFiscalSigef {
  numero: number;
  serie: number;
  numeroserie: string;
  cnpj: string;
  chave: string | null;
  valor: number;
  dataemissao: Date;
  codigosigef: number | null;
}

export interface NotaFiscalItem {
  id: number;
  notafiscal_id?: number;
  itemfiscal_id: number | null;
  ncm: string;
  descricao: string;
  quantidade: number;
  unidade: string;
  valorunitario: number | null;
  valor: number;
  itemfiscal: ItemFiscal | null;
}

export interface ItemFiscal {
  id: number | null;
  ncm: string | null;
  descricao: string;
  tipo: string;
  pesquisa: string;
}

export interface Municipio {
  id: number;
  nomeMunicipio: string;
}


const tamanhoCNPJSemDV: number = 12;
const regexCNPJSemDV: RegExp = /^([A-Z\d]){12}$/;
const regexCNPJ: RegExp = /^([A-Z\d]){12}(\d){2}$/;
const regexCaracteresMascara: RegExp = /[./-]/g;
const regexCaracteresNaoPermitidos: RegExp = /[^A-Z\d./-]/i;
const valorBase: number = "0".charCodeAt(0);
const pesosDV: number[] = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
const cnpjZerado: string = "00000000000000";

export function validarCnpj(cnpj: string): boolean {
  if (!regexCaracteresNaoPermitidos.test(cnpj)) {
    let cnpjSemMascara = removeMascaraCNPJ(cnpj);
    if (regexCNPJ.test(cnpjSemMascara) && cnpjSemMascara !== cnpjZerado) {
      const dvInformado = cnpjSemMascara.substring(tamanhoCNPJSemDV);
      const dvCalculado = calculaDV(cnpjSemMascara.substring(0, tamanhoCNPJSemDV));
      return dvInformado === dvCalculado;
    }
  }
  return false;
}

function calculaDV(cnpj: string): string {
  if (!regexCaracteresNaoPermitidos.test(cnpj)) {
    let cnpjSemMascara = removeMascaraCNPJ(cnpj);
    if (regexCNPJSemDV.test(cnpjSemMascara) && cnpjSemMascara !== cnpjZerado.substring(0, tamanhoCNPJSemDV)) {
      let somatorioDV1 = 0;
      let somatorioDV2 = 0;
      for (let i = 0; i < tamanhoCNPJSemDV; i++) {
        const asciiDigito = cnpjSemMascara.charCodeAt(i) - valorBase;
        somatorioDV1 += asciiDigito * pesosDV[i + 1];
        somatorioDV2 += asciiDigito * pesosDV[i];
      }
      const dv1 = somatorioDV1 % 11 < 2 ? 0 : 11 - (somatorioDV1 % 11);
      somatorioDV2 += dv1 * pesosDV[tamanhoCNPJSemDV];
      const dv2 = somatorioDV2 % 11 < 2 ? 0 : 11 - (somatorioDV2 % 11);
      return `${dv1}${dv2}`;
    }
  }
  throw new Error("Não é possível calcular o DV pois o CNPJ fornecido é inválido");
}

function removeMascaraCNPJ(cnpj: string): string {
  return cnpj.replace(regexCaracteresMascara, "");
}

export interface GastosPorNCM {
  id: string;
  ncm: string;
  descricao: string;
  quantidade: number;
  valor: number;
  tipo: string;
  dataTransacao: Date;
  unidadegestoraId: number;
  nmunidadegestora: string;
}
