export type PayloadLogin = PayloadLoginSau | PayloadLoginOtp;
export interface PayloadLoginSau {
    tipo: "sau";
    rota: string;
    ticket: string;
}
export interface PayloadLoginOtp {
    tipo: "otp";
    email: string;
    codigo: string;
}
export type TipoLogin = PayloadLogin["tipo"];
export interface RetornoLogin {
    credenciais: UsuarioAutenticado;
}
export interface RetornoLogout {
    message: string;
}
export interface RetornoCredenciais {
    credenciais: UsuarioAutenticado | null;
}
export interface UsuarioAutenticado {
    id: number;
    perfil: Perfil;
    nome: string;
    email: string;
    ipAddress: string | null;
    tipoLogin: TipoLogin;
    rotaInicial: string;
}
export declare enum Perfil {
    Portador = 0,
    GestorSed = 1,
    Gestor = 2,
    Consulta = 3,
    AdministradorCpesc = 4,
    AdministradorCiasc = 5
}
export declare enum HttpStatus {
    CONTINUE = 100,
    OK = 200,
    BAD_REQUEST = 400,
    UNAUTHORIZED = 401,
    FORBIDDEN = 403,
    NOT_FOUND = 404,
    NOT_ACCEPTABLE = 406,
    INTERNAL_SERVER_ERROR = 500
}

export interface CreditoCartao {
    id: number;
    dataCredito: Date;
    dataLimite: Date;
    preparacaopagamento: string;
    notaLancamento: string;
    valorCredito: number;
    prestacao: PrestacaoContas[];
    cartao: Cartao;
    subelemento: Subelemento;
    cdUnidadeGestora: string;
    unidadeGestora: UnidadeGestora;
    movimentacoes?: Movimentacao[];
    dataInicioMovimentacao: Date;
    dataLimiteMovimentacao: Date;
    temMaisLimites?: boolean;
}
export interface Credito extends CreditoCartao {
    notaEmpenho: string;
    ordemBancaria: string;
    fonteRecurso: string;
}
export interface UnidadeGestora {
    id: number;
    nome: string;
    codigo: number;
    descricao: string;
    cnpj: string;
    nomeOrdenador: string;
    contaBanco: ContaBanco;
}
export interface ContaBanco {
    conta: string;
    digitoConta: string;
}
export interface Portador {
    id: number;
    nome: string;
    nomeAbreviado: string;
    cpfOfuscado: string;
    email: string;
}
export interface Subelemento {
    id: number;
    subelemento: string;
    nome: string;
}
export interface PrestacaoContas {
    id: number;
    situacao: SituacaoPrestacaoConta;
    anoSgpe: string;
    orgaoSgpe: string;
    processoSgpe: string;
    valorGasto: number;
    limiteCartaoId: number;
    ip: string;
    ipCancelamento: string;
    motivoCancelamento: string;
    canceladoEm: Date;
    canceladoPor: string;
}
export declare enum SituacaoPrestacaoConta {
    Pendente = "P",
    Realizada = "R",
    Cancelada = "C"
}
export interface Cartao {
    id: number;
    numero: string;
    nuContaCartao: number;
    ativo: string;
    portadorUnidadeAdministrativa: PortadorUnidadeAdministrativa;
    movimentacoes: Movimentacao[];
}
export interface PortadorUnidadeAdministrativa {
    id: number;
    matricula: string;
    ativo: string;
    portador: Portador;
    unidadeAdministrativa: UnidadeAdministrativa;
}
export interface Movimentacao {
    id: number;
    numeroCartao: number;
    nomeEstabelecimento: string;
    CNPJEstabelecimento: string;
    cidadeEstabelecimento: string;
    ufEstabelecimento: string;
    valorTransacaoReal: number;
    dataTransacao: Date;
    horaTransacao: string;
    codigoTransacaoBB: number;
    descricaoTransacao: string;
    notaFiscal: NotaFiscal | null;
    reversao: ReversaoMovimentacao | null;
}
export interface UnidadeAdministrativa {
    id: number;
    nome: string;
    codigo: number;
    municipio: Municipio;
}
export interface NotaFiscal {
    id: number;
    movimentacao_id?: number | null;
    tiponotafiscal_id: TipoNotaFiscal;
    numero: number | null;
    numeroserie: string;
    serie: number | null;
    cnpj: string | null;
    chave: string | null;
    dataemissao: string | Date | null;
    valor: number | null;
    desconto: number | null;
    cofins: number | null;
    iss: number | null;
    pis: number | null;
    inss: number | null;
    ir: number | null;
    notafiscalitens: NotaFiscalItem[] | null;
    codigosigef: number | null;
    criadopor?: string;
    atualizadopor?: string;
}
export interface ReversaoMovimentacao {
    id: number;
    reversaoId: number;
    movimentacaoId: number;
    revertidoEm: Date;
    criadoem?: Date;
    atualizadoem?: Date;
    criadopor?: string;
    atualizadopor?: string;
    limitecartao_id?: number;
}
export declare enum TipoNotaFiscal {
    notaFiscalEletronicaImportacao = 1,
    notaFiscalEletronicaManual = 2,
    notaServicoManual = 3,
    cupomFiscalManual = 4,
    reversao = 5
}
export interface NotaFiscalSigef {
    numero: number;
    serie: number;
    numeroserie: string;
    cnpj: string;
    chave: string | null;
    valor: number;
    dataemissao: Date;
    codigosigef: number | null;
}
export interface NotaFiscalItem {
    id?: number;
    notafiscal_id?: number;
    itemfiscal_id: number | null;
    ncm: string;
    descricao: string;
    quantidade: number;
    unidade: string;
    valorunitario: number | null;
    valor: number;
    itemfiscal: ItemFiscal | null;
}
export interface ItemFiscal {
    id: number | null;
    ncm: string | null;
    descricao: string;
    tipo: string;
    pesquisa: string;
}
export interface Municipio {
    id: number;
    nomeMunicipio: string;
}
export declare function validarCnpj(cnpj: string): boolean;
