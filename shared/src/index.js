export var Perfil;
(function (Perfil) {
    Perfil[Perfil["Portador"] = 0] = "Portador";
    Perfil[Perfil["GestorSed"] = 1] = "GestorSed";
    Perfil[Perfil["Gestor"] = 2] = "Gestor";
    Perfil[Perfil["Consulta"] = 3] = "Consulta";
    Perfil[Perfil["AdministradorCpesc"] = 4] = "AdministradorCpesc";
    Perfil[Perfil["AdministradorCiasc"] = 5] = "AdministradorCiasc";
})(Perfil || (Perfil = {}));
export var HttpStatus;
(function (HttpStatus) {
    HttpStatus[HttpStatus["CONTINUE"] = 100] = "CONTINUE";
    HttpStatus[HttpStatus["OK"] = 200] = "OK";
    HttpStatus[HttpStatus["BAD_REQUEST"] = 400] = "BAD_REQUEST";
    HttpStatus[HttpStatus["UNAUTHORIZED"] = 401] = "UNAUTHORIZED";
    HttpStatus[HttpStatus["FORBIDDEN"] = 403] = "FORBIDDEN";
    HttpStatus[HttpStatus["NOT_FOUND"] = 404] = "NOT_FOUND";
    HttpStatus[HttpStatus["NOT_ACCEPTABLE"] = 406] = "NOT_ACCEPTABLE";
    HttpStatus[HttpStatus["INTERNAL_SERVER_ERROR"] = 500] = "INTERNAL_SERVER_ERROR";
})(HttpStatus || (HttpStatus = {}));
export var SituacaoPrestacaoConta;
(function (SituacaoPrestacaoConta) {
    SituacaoPrestacaoConta["Pendente"] = "P";
    SituacaoPrestacaoConta["Realizada"] = "R";
    SituacaoPrestacaoConta["Cancelada"] = "C";
})(SituacaoPrestacaoConta || (SituacaoPrestacaoConta = {}));
export var TipoNotaFiscal;
(function (TipoNotaFiscal) {
    TipoNotaFiscal[TipoNotaFiscal["notaFiscalEletronicaImportacao"] = 1] = "notaFiscalEletronicaImportacao";
    TipoNotaFiscal[TipoNotaFiscal["notaFiscalEletronicaManual"] = 2] = "notaFiscalEletronicaManual";
    TipoNotaFiscal[TipoNotaFiscal["notaServicoManual"] = 3] = "notaServicoManual";
    TipoNotaFiscal[TipoNotaFiscal["cupomFiscalManual"] = 4] = "cupomFiscalManual";
    TipoNotaFiscal[TipoNotaFiscal["reversao"] = 5] = "reversao";
})(TipoNotaFiscal || (TipoNotaFiscal = {}));
const tamanhoCNPJSemDV = 12;
const regexCNPJSemDV = /^([A-Z\d]){12}$/;
const regexCNPJ = /^([A-Z\d]){12}(\d){2}$/;
const regexCaracteresMascara = /[./-]/g;
const regexCaracteresNaoPermitidos = /[^A-Z\d./-]/i;
const valorBase = "0".charCodeAt(0);
const pesosDV = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
const cnpjZerado = "00000000000000";
export function validarCnpj(cnpj) {
    if (!regexCaracteresNaoPermitidos.test(cnpj)) {
        let cnpjSemMascara = removeMascaraCNPJ(cnpj);
        if (regexCNPJ.test(cnpjSemMascara) && cnpjSemMascara !== cnpjZerado) {
            const dvInformado = cnpjSemMascara.substring(tamanhoCNPJSemDV);
            const dvCalculado = calculaDV(cnpjSemMascara.substring(0, tamanhoCNPJSemDV));
            return dvInformado === dvCalculado;
        }
    }
    return false;
}
function calculaDV(cnpj) {
    if (!regexCaracteresNaoPermitidos.test(cnpj)) {
        let cnpjSemMascara = removeMascaraCNPJ(cnpj);
        if (regexCNPJSemDV.test(cnpjSemMascara) && cnpjSemMascara !== cnpjZerado.substring(0, tamanhoCNPJSemDV)) {
            let somatorioDV1 = 0;
            let somatorioDV2 = 0;
            for (let i = 0; i < tamanhoCNPJSemDV; i++) {
                const asciiDigito = cnpjSemMascara.charCodeAt(i) - valorBase;
                somatorioDV1 += asciiDigito * pesosDV[i + 1];
                somatorioDV2 += asciiDigito * pesosDV[i];
            }
            const dv1 = somatorioDV1 % 11 < 2 ? 0 : 11 - (somatorioDV1 % 11);
            somatorioDV2 += dv1 * pesosDV[tamanhoCNPJSemDV];
            const dv2 = somatorioDV2 % 11 < 2 ? 0 : 11 - (somatorioDV2 % 11);
            return `${dv1}${dv2}`;
        }
    }
    throw new Error("Não é possível calcular o DV pois o CNPJ fornecido é inválido");
}
function removeMascaraCNPJ(cnpj) {
    return cnpj.replace(regexCaracteresMascara, "");
}
//# sourceMappingURL=index.js.map