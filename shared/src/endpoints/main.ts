import { z, ZodType } from "zod";
import { endpointsAuth } from "./auth.js";
import { endpointsItemFiscal } from "./item-fiscal.js";
import { endpointsLimiteCartao } from "./limite-cartao.js";
import { endpointsMovimentacao } from "./movimentacao.js";
import { endpointsNotaFiscalItem } from "./nota-fiscal-item.js";
import { endpointsNotaFiscal } from "./nota-fiscal.js";
import { endpointsPortador } from "./portador.js";
import { endpointsPrestacaoContas } from "./prestacao-contas.js";
import { endpointsReversaoMovimentacao } from "./reversao-movimentacao.js";
import { endpointsUnidadeAdministrativa } from "./unidade-administrativa.js";
import { endpointsUnidadeGestora } from "./unidade-gestora.js";

export type MetodoHttp = "get" | "post" | "put" | "delete" | "patch" | "options" | "head";

export interface Rota<
  TUri extends string = string,
  TMetodo extends MetodoHttp = MetodoHttp,
  TPayload extends ZodType = ZodType,
  TResposta extends ZodType = ZodType,
> {
  uri: TUri;
  metodo: TMetodo;
  tipoPayload: TPayload;
  tipoResposta: TResposta;
}

export type RespostaRota<T extends Rota> = Promise<z.infer<T["tipoResposta"]>>;

export const endpoints = {
  ...endpointsAuth,
  ...endpointsItemFiscal,
  ...endpointsLimiteCartao,
  ...endpointsMovimentacao,
  ...endpointsNotaFiscal,
  ...endpointsNotaFiscalItem,
  ...endpointsPortador,
  ...endpointsPrestacaoContas,
  ...endpointsReversaoMovimentacao,
  ...endpointsUnidadeAdministrativa,
  ...endpointsUnidadeGestora,
} as const satisfies Record<string, Rota>;
