import { z, ZodType } from "zod";
import { ReversaoMovimentacao } from "../index.js";
import { Rota } from "./main.js";

export const reversaoMovimentacaoSchema: ZodType<ReversaoMovimentacao> = z.object({
  id: z.number(),
  reversaoId: z.number(),
  movimentacaoId: z.number(),
  revertidoEm: z.date(),
  criadoem: z.date().optional(),
  atualizadoem: z.date().optional(),
  criadopor: z.string().optional(),
  atualizadopor: z.string().optional(),
  limitecartao_id: z.number().optional(),
});

export const payloadCriarReversaoMovimentacaoSchema = z.object({
  reversaoId: z.number(),
  movimentacaoId: z.number(),
  revertidoEm: z.date(),
  limitecartao_id: z.number(),
});

export type PayloadCriarReversaoMovimentacao = z.infer<typeof payloadCriarReversaoMovimentacaoSchema>;

export const endpointsReversaoMovimentacao = {
  criarReversaoMovimentacao: {
    uri: "/reversao-movimentacao",
    metodo: "post",
    tipoPayload: payloadCriarReversaoMovimentacaoSchema,
    tipoResposta: z.object({
      sucesso: z.boolean(),
      mensagem: z.string(),
    }),
  },
  excluirReversaoMovimentacao: {
    uri: "/reversao-movimentacao/:id",
    metodo: "delete",
    tipoPayload: z.undefined(),
    tipoResposta: z.object({
      sucesso: z.boolean(),
    }),
  },
} as const satisfies Record<string, Rota>;
