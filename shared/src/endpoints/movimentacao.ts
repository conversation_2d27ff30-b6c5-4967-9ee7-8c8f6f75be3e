import { z, ZodType } from "zod";
import { Movimentacao } from "../index.js";
import { Rota } from "./main.js";
import { notaFiscalSchema } from "./nota-fiscal.js";
import { reversaoMovimentacaoSchema } from "./reversao-movimentacao.js";

export const movimentacaoSchema: ZodType<Movimentacao> = z.object({
  id: z.number(),
  numeroCartao: z.number(),
  nomeEstabelecimento: z.string(),
  CNPJEstabelecimento: z.string(),
  cidadeEstabelecimento: z.string(),
  ufEstabelecimento: z.string(),
  valorTransacaoReal: z.number(),
  dataTransacao: z.date(),
  horaTransacao: z.string(),
  indicativoDebitoCredito: z.string(),
  codigoTransacaoBB: z.number(),
  descricaoTransacao: z.string(),
  notaFiscal: z.nullable(notaFiscalSchema),
  reversao: z.nullable(reversaoMovimentacaoSchema),
});

export const endpointsMovimentacao = {
  excluirVinculo: {
    uri: "/movimentacao/:id",
    metodo: "patch",
    tipoPayload: z.undefined(),
    tipoResposta: z.object({
      sucesso: z.boolean(),
    }),
  },
  associarLimiteMovimentacao: {
    uri: "/movimentacao/associar-limite",
    metodo: "patch",
    tipoPayload: z.object({
      movimentacaoIds: z.array(z.number()),
      limiteCartaoId: z.number(),
    }),
    tipoResposta: z.object({
      sucesso: z.boolean(),
    }),
  },
} as const satisfies Record<string, Rota>;
