import { z, ZodType } from "zod";
import { NotaFiscal, NotaFiscalSigef } from "../index.js";
import type { Rota } from "./main.js";
import { notaFiscalItemSchema, payloadCriarNFItemSchema } from "./nota-fiscal-item.js";

export const payloadCriarNFImportacaoSchema = z.object({
  chave: z.string().nullable(),
  cnpj: z.string(),
  codigosigef: z.number(),
  dataemissao: z.string(),
  movimentacao_id: z.number(),
  numero: z.number(),
  serie: z.number(),
  tiponotafiscal_id: z.number(),
  valor: z.number().nullable(),
});

export const payloadCriarNFManualSchema = z.object({
  movimentacao_id: z.number(),
  tiponotafiscal_id: z.number(),
  numero: z.number(),
  serie: z.number().nullable(),
  cnpj: z.string(),
  valor: z.number(),
  desconto: z.number().nullable(),
  cofins: z.number().nullable(),
  iss: z.number().nullable(),
  pis: z.number().nullable(),
  inss: z.number().nullable(),
  ir: z.number().nullable(),
  dataemissao: z.string(),
  notafiscalitens: z.array(payloadCriarNFItemSchema).nullable(),
});

export const notaFiscalSchema: ZodType<NotaFiscal> = z.object({
  id: z.number(),
  movimentacao_id: z.number().nullable().optional(),
  tiponotafiscal_id: z.number(),
  numero: z.number().nullable(),
  numeroserie: z.string(),
  serie: z.number().nullable(),
  cnpj: z.string().nullable(),
  chave: z.string().nullable(),
  dataemissao: z.union([z.string(), z.date()]).nullable(),
  valor: z.number().nullable(),
  desconto: z.number().nullable(),
  cofins: z.number().nullable(),
  iss: z.number().nullable(),
  pis: z.number().nullable(),
  inss: z.number().nullable(),
  ir: z.number().nullable(),
  notafiscalitens: z.array(notaFiscalItemSchema).nullable(),
  codigosigef: z.number().nullable(),
  criadopor: z.string().optional(),
  atualizadopor: z.string().optional(),
});

export const notaFiscalSigefSchema: ZodType<NotaFiscalSigef> = z.object({
  numero: z.number(),
  serie: z.number(),
  numeroserie: z.string(),
  cnpj: z.string(),
  chave: z.string().nullable(),
  valor: z.number(),
  dataemissao: z.date(),
  codigosigef: z.number().nullable(),
});

export const payloadCriarNFSchema = z.union([payloadCriarNFImportacaoSchema, payloadCriarNFManualSchema]);
export type PayloadCriarNF = z.infer<typeof payloadCriarNFSchema>;
export type PayloadCriarNFImportacao = z.infer<typeof payloadCriarNFImportacaoSchema>;
export type PayloadCriarNFManual = z.infer<typeof payloadCriarNFManualSchema>;

export const endpointsNotaFiscal = {
  criarNotaFiscal: {
    uri: "/nota-fiscal",
    metodo: "post",
    tipoPayload: payloadCriarNFSchema,
    tipoResposta: z.object({
      sucesso: z.boolean(),
      mensagem: z.string(),
      idNotaFiscalGerada: z.number(),
    }),
  },
  buscarNotasFiscais: {
    uri: "/nota-fiscal",
    metodo: "get",
    tipoPayload: z.undefined(),
    tipoResposta: z.array(notaFiscalSchema),
  },
  buscarNotasFiscaisExternas: {
    uri: "/nota-fiscal/buscar-externo/:parametro",
    metodo: "get",
    tipoPayload: z.undefined(),
    tipoResposta: z.array(notaFiscalSigefSchema),
  },
  editarNotaFiscal: {
    uri: "/nota-fiscal/:id",
    metodo: "put",
    tipoPayload: notaFiscalSchema,
    tipoResposta: z.object({
      sucesso: z.boolean(),
      mensagem: z.string(),
    }),
  },
} as const satisfies Record<string, Rota>;
