import { z, ZodType } from "zod";
import { ContaBanco, UnidadeGestora } from "../index.js";
import type { Rota } from "./main.js";

export const contaBancoSchema: ZodType<ContaBanco> = z.object({
  conta: z.string(),
  digitoConta: z.string(),
});

export const unidadeGestoraSchema: ZodType<UnidadeGestora> = z.object({
  id: z.number(),
  nome: z.string(),
  codigo: z.number(),
  descricao: z.string(),
  cnpj: z.string(),
  nomeOrdenador: z.string(),
  contaBanco: contaBancoSchema,
});

export const endpointsUnidadeGestora = {
  buscarUnidadesGestoras: {
    uri: "/unidade-gestora",
    metodo: "get",
    tipoPayload: z.undefined(),
    tipoResposta: z.array(unidadeGestoraSchema),
  },
  buscarUnidadesGestorasPorUsuario: {
    uri: "/unidade-gestora/usuario",
    metodo: "get",
    tipoPayload: z.undefined(),
    tipoResposta: z.array(unidadeGestoraSchema),
  },
} as const satisfies Record<string, Rota>;
