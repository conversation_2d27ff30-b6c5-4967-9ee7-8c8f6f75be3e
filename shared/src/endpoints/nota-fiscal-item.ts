import { z, ZodType } from "zod";
import { NotaFiscalItem } from "../index.js";
import { itemFiscalSchema } from "./item-fiscal.js";
import { Rota } from "./main.js";

export const payloadCriarItemFiscalSchema = z.object({
  id: z.number(),
  ncm: z.string(),
  descricao: z.string(),
  tipo: z.string(),
  pesquisa: z.string(),
});

export const payloadCriarNFItemSchema = z.object({
  itemfiscal_id: z.number().nullable(),
  ncm: z.string(),
  descricao: z.string(),
  unidade: z.string(),
  quantidade: z.number(),
  valor: z.number(),
  valorunitario: z.number().nullable(),
  itemfiscal: payloadCriarItemFiscalSchema.nullable(),
});

export const notaFiscalItemSchema: ZodType<NotaFiscalItem> = z.object({
  id: z.number(),
  notafiscal_id: z.number().optional(),
  itemfiscal_id: z.number().nullable(),
  ncm: z.string(),
  descricao: z.string(),
  quantidade: z.number(),
  unidade: z.string(),
  valorunitario: z.number().nullable(),
  valor: z.number(),
  itemfiscal: itemFiscalSchema.nullable(),
});

export type PayloadCriarNFItem = z.infer<typeof payloadCriarNFItemSchema>;

export const endpointsNotaFiscalItem = {} as const satisfies Record<string, Rota>;
