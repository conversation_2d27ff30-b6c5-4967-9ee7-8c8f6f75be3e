import { z, ZodType } from "zod";
import { PrestacaoContas, SituacaoPrestacaoConta } from "../index.js";
import { Rota } from "./main.js";

export const prestacaoContasSchema: ZodType<PrestacaoContas> = z.object({
  id: z.number(),
  situacao: z.nativeEnum(SituacaoPrestacaoConta),
  anoSgpe: z.string(),
  orgaoSgpe: z.string(),
  processoSgpe: z.string(),
  valorGasto: z.number(),
  limiteCartaoId: z.number(),
  ip: z.string(),
  ipCancelamento: z.string(),
  motivoCancelamento: z.string(),
  canceladoEm: z.date(),
  canceladoPor: z.string(),
});

export const endpointsPrestacaoContas = {
  criarPrestacaoContas: {
    uri: "/prestacao-contas",
    metodo: "post",
    tipoPayload: z.object({
      limiteCartaoId: z.number(),
      valorGasto: z.number(),
      orgaoSgpe: z.string(),
      processoSgpe: z.string(),
      anoSgpe: z.string(),
    }),
    tipoResposta: prestacaoContasSchema,
  },
  cancelarPrestacaoContas: {
    uri: "/prestacao-contas",
    metodo: "patch",
    tipoPayload: z.object({
      prestacaoId: z.number(),
      motivoCancelamento: z.string(),
    }),
    tipoResposta: z.boolean(),
  },
} as const satisfies Record<string, Rota>;
