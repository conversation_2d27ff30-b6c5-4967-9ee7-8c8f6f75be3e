﻿/*CREATE TABLE CPESC_UNIDADEGESTORA AS SELECT UG.*, CASE FL_STATUS WHEN 1 THEN 'S' ELSE 'N' END ATIVO, CASE FL_ADICIONAR WHEN 1 THEN 'S' ELSE 'N' END ADICIONARITEMPC
FROM UNIDADEGESTORA UG;
ALTER TABLE CPESC_UNIDADEGESTORA DROP COLUMN NM_GESTAO;
ALTER TABLE CPESC_UNIDADEGESTORA DROP COLUMN NM_PASTAPRESTADORES;
ALTER TABLE CPESC_UNIDADEGESTORA DROP COLUMN NM_USUARIOFTP;
ALTER TABLE CPESC_UNIDADEGESTORA DROP COLUMN NM_SENHAFTP;
ALTER TABLE CPESC_UNIDADEGESTORA DROP COLUMN NM_PASTAFTP;
ALTER TABLE CPESC_UNIDADEGESTORA DROP COLUMN NM_PASTAENVIOBANCO;
ALTER TABLE CPESC_UNIDADEGESTORA DROP COLUMN NM_PASTARETORNOBANCO;
ALTER TABLE CPESC_UNIDADEGESTORA DROP COLUMN CD_MUNICIPIO;
ALTER TABLE CPESC_UNIDADEGESTORA DROP COLUMN FL_STATUS;
ALTER TABLE CPESC_UNIDADEGESTORA DROP COLUMN FL_ADICIONAR;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN NM_SIGLASGPE TO SIGLASGPE;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN CD_UNIDADEGESTORA TO CODIGO;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN CD_GESTAO TO CODIGOGESTAO;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN NM_UNIDADEGESTORA TO NOME;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN NM_ORDENADOR TO NOMEORDENADOR;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN NU_CNPJ TO CNPJ;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN NM_LOGRADOURO TO NOMELOGRADOURO;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN NU_LOGRADOURO TO NUMEROLOGRADOURO;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN NM_COMPLEMENTO TO COMPLEMENTO;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN NM_BAIRRO TO BAIRRO;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN NM_CIDADE TO CIDADE;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN NU_CEP TO CEP;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN NU_DDD TO DDD;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN NU_FONE TO FONE;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN NM_ABREVIADO TO NOMEABREVIADO;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN NM_DESCRICAO TO DESCRICAO;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN UPDATED_AT TO ATUALIZADOEM;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN UPDATED_BY TO ATUALIZADOPOR;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN CREATED_AT TO CRIADOEM;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN CREATED_BY TO CRIADOPOR;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN FL_BLOQUEIO TO BLOQUEIO_ID;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN FL_CALCULOPAGAMENTO TO RATEIO;
ALTER TABLE CPESC_UNIDADEGESTORA MODIFY RATEIO DEFAULT 0;
ALTER TABLE CPESC_UNIDADEGESTORA RENAME COLUMN LT_CPFAIGNORAR TO LTCPFAIGNORAR;

ALTER TABLE CPESC_UNIDADEGESTORA MODIFY CRIADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE CPESC_UNIDADEGESTORA MODIFY ATUALIZADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
*/
CREATE TABLE CPESC_UNIDADEGESTORA
(
  "ID" NUMBER NOT NULL ENABLE,
  "CODIGO" NUMBER(6,0) NOT NULL ENABLE,
  "CODIGOGESTAO" NUMBER(5,0) NOT NULL ENABLE,
  "NOME" VARCHAR2(255) NOT NULL ENABLE,
  "NOMEORDENADOR" VARCHAR2(50),
  "CNPJ" VARCHAR2(14),
  "NOMELOGRADOURO" VARCHAR2(100),
  "NUMEROLOGRADOURO" VARCHAR2(5),
  "COMPLEMENTO" VARCHAR2(50),
  "BAIRRO" VARCHAR2(50),
  "CIDADE" VARCHAR2(75),
  "CEP" VARCHAR2(9),
  "DDD" VARCHAR2(4),
  "FONE" VARCHAR2(9),
  "NOMEABREVIADO" VARCHAR2(15),
  "MUNICIPIO_ID" NUMBER,
  "DESCRICAO" VARCHAR2(15),
  "ATUALIZADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "ATUALIZADOPOR" VARCHAR2(255),
  "CRIADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "CRIADOPOR" VARCHAR2(255),
  "BLOQUEIO_ID" NUMBER(1,0),
  "RATEIO" NUMBER(2,0) DEFAULT 0,
  "SIGLASGPE" VARCHAR2(20),
  "LTCPFAIGNORAR" VARCHAR2(4000),
  "ATIVO" CHAR(1),
  "ADICIONARITEMPC" CHAR(1)
);

ALTER TABLE CPESC_UNIDADEGESTORA ADD CONSTRAINT CPESC_UNIDADEGESTORA_PK PRIMARY KEY (ID);

CREATE SEQUENCE CPESC_UNIDADEGESTORA_ID_SEQ START WITH 52 INCREMENT BY 1 NOCACHE ORDER;

CREATE OR REPLACE TRIGGER CPESC_UNIDADEGESTORA_ID_TRG BEFORE
  INSERT ON CPESC_UNIDADEGESTORA FOR EACH ROW    WHEN (NEW.ID IS NULL) BEGIN :NEW.ID := CPESC_UNIDADEGESTORA_ID_SEQ.NEXTVAL;
END;


--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
CREATE TABLE  CPESC_UNIDADEGESTORA_BLOQUEIO(
  ID        NUMBER,
  DESCRICAO VARCHAR2(50),
  CONSTRAINT CPESC_UNIDADEGESTORA_BLOQUEIO_PK PRIMARY KEY (ID)
);

INSERT INTO CPESC_UNIDADEGESTORA_BLOQUEIO VALUES (0, 'Operando Manual');
INSERT INTO CPESC_UNIDADEGESTORA_BLOQUEIO VALUES (1, 'Operando com Automa��o');
INSERT INTO CPESC_UNIDADEGESTORA_BLOQUEIO VALUES (2, 'Bloqueado');
COMMIT;
--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
CREATE TABLE  CPESC_PERFIL(
  ID        NUMBER,
  NOME VARCHAR2(50),
  CONSTRAINT CPESC_PERFIL_PK PRIMARY KEY (ID)
);

INSERT INTO CPESC_PERFIL VALUES (1,'GESTOR SED');
INSERT INTO CPESC_PERFIL VALUES (2,'GESTOR');
INSERT INTO CPESC_PERFIL VALUES (3,'CONSULTA');
INSERT INTO CPESC_PERFIL VALUES (4,'ADMINISTRADOR CPESC');
INSERT INTO CPESC_PERFIL VALUES (5,'ADMINISTRADOR CIASC');
COMMIT;


--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
/*CREATE TABLE CPESC_USUARIO AS SELECT U.*, CASE FL_STATUS WHEN 1 THEN 'S' ELSE 'N' END ATIVO, CASE FL_EMAIL WHEN 1 THEN 'S' ELSE 'N' END RECEBEEMAIL FROM USUARIO U;
ALTER TABLE CPESC_USUARIO DROP COLUMN NM_SENHA;
ALTER TABLE CPESC_USUARIO DROP COLUMN CD_UNIDADE_GESTORA;
ALTER TABLE CPESC_USUARIO DROP COLUMN CD_GESTAO;
ALTER TABLE CPESC_USUARIO DROP COLUMN PERMISSIONS;
ALTER TABLE CPESC_USUARIO DROP COLUMN ACTIVATED;
ALTER TABLE CPESC_USUARIO DROP COLUMN ACTIVATION_CODE;
ALTER TABLE CPESC_USUARIO DROP COLUMN ACTIVATED_AT;
ALTER TABLE CPESC_USUARIO DROP COLUMN LAST_LOGIN;
ALTER TABLE CPESC_USUARIO DROP COLUMN PERSIST_CODE;
ALTER TABLE CPESC_USUARIO DROP COLUMN RESET_PASSWORD_CODE;
ALTER TABLE CPESC_USUARIO DROP COLUMN FIRST_NAME;
ALTER TABLE CPESC_USUARIO DROP COLUMN LAST_NAME;
ALTER TABLE CPESC_USUARIO DROP COLUMN UNIDADEGESTORA_ID;
ALTER TABLE CPESC_USUARIO DROP COLUMN NM_PAGINAINICIAL;
ALTER TABLE CPESC_USUARIO DROP COLUMN FL_STATUS;
ALTER TABLE CPESC_USUARIO DROP COLUMN FL_EMAIL;
ALTER TABLE CPESC_USUARIO DROP COLUMN NU_CPF;
ALTER TABLE CPESC_USUARIO RENAME COLUMN NM_USUARIO TO NOME;
ALTER TABLE CPESC_USUARIO RENAME COLUMN CREATED_AT TO CRIADOEM;
ALTER TABLE CPESC_USUARIO RENAME COLUMN UPDATED_AT TO ATUALIZADOEM;
ALTER TABLE CPESC_USUARIO RENAME COLUMN CREATED_BY TO CRIADOPOR;
ALTER TABLE CPESC_USUARIO RENAME COLUMN UPDATED_BY TO ATUALIZADOPOR;
ALTER TABLE CPESC_USUARIO ADD ULTIMOACESSO DATE;
ALTER TABLE CPESC_USUARIO ADD CODIGOOTP VARCHAR2(60);
ALTER TABLE CPESC_USUARIO ADD EXPIRACAOOTP TIMESTAMP;

ALTER TABLE CPESC_USUARIO MODIFY CRIADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE CPESC_USUARIO MODIFY ATUALIZADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
DROP SEQUENCE CPESC_USUARIO_ID_SEQ;
*/

CREATE TABLE CPESC_USUARIO
 (
  "ID" NUMBER NOT NULL ENABLE,
  "NOME" VARCHAR2(255),
  "CPF" VARCHAR2(11),
  "EMAIL" VARCHAR2(255) NOT NULL ENABLE,
  "CRIADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "ATUALIZADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "RECEBEEMAIL" CHAR,
  "PERFIL_ID" NUMBER(1,0),
  "CRIADOPOR" VARCHAR2(255),
  "ATUALIZADOPOR" VARCHAR2(255),
  "ATIVO" CHAR(1),
  "ULTIMOACESSO" DATE,
  "CODIGOOTP" VARCHAR2(60),
  "EXPIRACAOOTP" TIMESTAMP (6)
   ) ;
ALTER TABLE CPESC_USUARIO ADD CONSTRAINT CPESC_USUARIO_PK PRIMARY KEY (ID);
ALTER TABLE CPESC_USUARIO ADD CONSTRAINT CPESC_USUARIO_PERFIL_FK FOREIGN KEY (PERFIL_ID) REFERENCES CPESC_PERFIL (ID);

CREATE SEQUENCE CPESC_USUARIO_ID_SEQ START WITH 245 INCREMENT BY 1 NOCACHE ORDER;

CREATE OR REPLACE TRIGGER CPESC_USUARIO_ID_TRG BEFORE
  INSERT ON CPESC_USUARIO FOR EACH ROW WHEN (NEW.ID IS NULL) BEGIN :NEW.ID := CPESC_USUARIO_ID_SEQ.NEXTVAL;
END;

UPDATE CPESC_USUARIO
SET perfil_id = 5
WHERE ID IN (
SELECT U.ID from USUARIO U , USUARIO_GRUPO UG WHERE UG.GRUPO_ID = 1 AND U.ID = UG.USUARIO_ID);
COMMIT;

--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
/* CREATE TABLE CPESC_USUARIO_UNIDADEGESTORA AS SELECT * FROM USUARIO_UNIDADEGESTORA; */
CREATE TABLE CPESC_USUARIO_UNIDADEGESTORA
(
  USUARIO_ID        INTEGER NOT NULL,
  UNIDADEGESTORA_ID INTEGER
);

ALTER TABLE CPESC_USUARIO_UNIDADEGESTORA ADD CONSTRAINT CPESC_USUARIO_UNIDADEGESTORA_UG_FK FOREIGN KEY (UNIDADEGESTORA_ID)
	  REFERENCES CPESC_UNIDADEGESTORA (ID) ON DELETE CASCADE;

ALTER TABLE CPESC_USUARIO_UNIDADEGESTORA ADD  CONSTRAINT CPESC_USUARIO_UNIDADEGESTORA_USUARIO_FK FOREIGN KEY (USUARIO_ID)
	  REFERENCES CPESC_USUARIO (ID) ON DELETE CASCADE;

--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
/*CREATE TABLE CPESC_MUNICIPIO AS SELECT * FROM MUNICIPIO;
alter table CPESC_MUNICIPIO rename column cd_municipio to CODIGO;
alter table CPESC_MUNICIPIO rename column nm_municipio to NOME;
alter table CPESC_MUNICIPIO rename column cd_ibge to CODIGOIBGE;
alter table CPESC_MUNICIPIO drop column nm_abreviado; */

CREATE TABLE CPESC_MUNICIPIO
(
  "ID" NUMBER NOT NULL ENABLE,
	"CODIGO" NUMBER,
	"NOME" VARCHAR2(100),
	"CODIGOIBGE" NUMBER
);
ALTER TABLE CPESC_MUNICIPIO ADD CONSTRAINT CPESC_MUNICIPIO_PK PRIMARY KEY (ID);

--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
/*CREATE TABLE CPESC_UNIDADEADMINISTRATIVA AS SELECT U.*, CASE FL_DEFAULT WHEN 1 THEN 'S' ELSE 'N' END ATIVO FROM UNIDADEADMINISTRATIVA U;
ALTER TABLE CPESC_UNIDADEADMINISTRATIVA DROP COLUMN DELETED_AT;
ALTER TABLE CPESC_UNIDADEADMINISTRATIVA DROP COLUMN QT_ALUNOS;
ALTER TABLE CPESC_UNIDADEADMINISTRATIVA DROP COLUMN VL_ANUAL;
ALTER TABLE CPESC_UNIDADEADMINISTRATIVA DROP COLUMN QT_PARCELAS;
ALTER TABLE CPESC_UNIDADEADMINISTRATIVA DROP COLUMN FL_DEFAULT;
ALTER TABLE CPESC_UNIDADEADMINISTRATIVA RENAME COLUMN NM_UNIDADEADMINISTRATIVA TO NOME;
ALTER TABLE CPESC_UNIDADEADMINISTRATIVA RENAME COLUMN NM_ENDERECO TO ENDERECO;
ALTER TABLE CPESC_UNIDADEADMINISTRATIVA RENAME COLUMN NU_CEP TO CEP;
ALTER TABLE CPESC_UNIDADEADMINISTRATIVA RENAME COLUMN CD_UNIDADEADMINISTRATIVA TO CODIGO;
ALTER TABLE CPESC_UNIDADEADMINISTRATIVA RENAME COLUMN NM_BAIRRO TO BAIRRO;
ALTER TABLE CPESC_UNIDADEADMINISTRATIVA RENAME COLUMN NM_CIDADE TO CIDADE;
ALTER TABLE CPESC_UNIDADEADMINISTRATIVA RENAME COLUMN NU_CNPJ TO CNPJ;
ALTER TABLE CPESC_UNIDADEADMINISTRATIVA RENAME COLUMN CONVENIOBANCO_ID TO CONTABANCO_ID;
ALTER TABLE CPESC_UNIDADEADMINISTRATIVA RENAME COLUMN CREATED_BY TO CRIADOPOR;
ALTER TABLE CPESC_UNIDADEADMINISTRATIVA RENAME COLUMN CREATED_AT TO CRIADOEM;
ALTER TABLE CPESC_UNIDADEADMINISTRATIVA RENAME COLUMN UPDATED_BY TO ATUALIZADOPOR;
ALTER TABLE CPESC_UNIDADEADMINISTRATIVA RENAME COLUMN UPDATED_AT TO ATUALIZADOEM;

ALTER TABLE CPESC_UNIDADEADMINISTRATIVA MODIFY CRIADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE CPESC_UNIDADEADMINISTRATIVA MODIFY ATUALIZADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
*/

CREATE TABLE CPESC_UNIDADEADMINISTRATIVA
(
  "ID" NUMBER NOT NULL ENABLE,
  "NOME" VARCHAR2(255) NOT NULL ENABLE,
  "ENDERECO" VARCHAR2(100),
  "CEP" VARCHAR2(8),
  "UNIDADEGESTORA_ID" NUMBER NOT NULL ENABLE,
  "CODIGO" NUMBER,
  "BAIRRO" VARCHAR2(70),
  "MUNICIPIO_ID" NUMBER,
  "CIDADE" VARCHAR2(70),
  "CNPJ" VARCHAR2(14),
  "CONTABANCO_ID" NUMBER,
  "CRIADOPOR" VARCHAR2(255),
  "CRIADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "ATUALIZADOPOR" VARCHAR2(255),
  "ATUALIZADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "ATIVO" CHAR(1)
)

comment on column CPESC_UNIDADEADMINISTRATIVA.CODIGO is 'Somente para SED';

ALTER TABLE CPESC_UNIDADEADMINISTRATIVA ADD CONSTRAINT CPESC_UNIDADEADMINISTRATIVA_PK PRIMARY KEY (ID);

ALTER TABLE CPESC_UNIDADEADMINISTRATIVA ADD CONSTRAINT CPESC_UNIDADEADMINISTRATIVA_UG_FK FOREIGN KEY (UNIDADEGESTORA_ID) REFERENCES CPESC_UNIDADEGESTORA (ID);

CREATE SEQUENCE CPESC_UNIDADEADMINISTRATIVA_ID_SEQ START WITH 10878 INCREMENT BY 1 NOCACHE ORDER;

CREATE OR REPLACE TRIGGER CPESC_UNIDADEADMINISTRATIVA_ID_TRG BEFORE
  INSERT ON CPESC_UNIDADEADMINISTRATIVA FOR EACH ROW  WHEN (NEW.ID IS NULL) BEGIN :NEW.ID := CPESC_UNIDADEADMINISTRATIVA_ID_SEQ.NEXTVAL;
END;

--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
/*CREATE TABLE  CPESC_PORTADORCARTAO AS SELECT P.* FROM PORTADORCARTAO P WHERE ID IN (SELECT MAX(ID) FROM PORTADORCARTAO GROUP BY NU_CPF);
ALTER TABLE CPESC_PORTADORCARTAO DROP COLUMN UNIDADEADMINISTRATIVA_ID;
ALTER TABLE CPESC_PORTADORCARTAO DROP COLUMN VALOR;
ALTER TABLE CPESC_PORTADORCARTAO DROP COLUMN LIMITE_MATERIAL;
ALTER TABLE CPESC_PORTADORCARTAO DROP COLUMN LIMITE_SERVICO;
ALTER TABLE CPESC_PORTADORCARTAO DROP COLUMN CENTROCUSTO_ID;
ALTER TABLE CPESC_PORTADORCARTAO DROP COLUMN UNIDADEGESTORA_ID;
ALTER TABLE CPESC_PORTADORCARTAO DROP COLUMN FL_STATUS;
ALTER TABLE CPESC_PORTADORCARTAO RENAME COLUMN NU_MATRICULA TO MATRICULA;
ALTER TABLE CPESC_PORTADORCARTAO DROP COLUMN DT_ALTERACAO_STATUS;
ALTER TABLE CPESC_PORTADORCARTAO RENAME COLUMN NM_EMAIL TO EMAIL;
ALTER TABLE CPESC_PORTADORCARTAO RENAME COLUMN NU_CPF TO CPF;
ALTER TABLE CPESC_PORTADORCARTAO RENAME COLUMN NM_PORTADOR TO NOME;
ALTER TABLE CPESC_PORTADORCARTAO RENAME COLUMN DT_NASCIMENTO TO DATANASCIMENTO;
ALTER TABLE CPESC_PORTADORCARTAO RENAME COLUMN NM_ABREVIADO TO NOMEABREVIADO;
ALTER TABLE CPESC_PORTADORCARTAO RENAME COLUMN CREATED_BY TO CRIADOPOR;
ALTER TABLE CPESC_PORTADORCARTAO RENAME COLUMN CREATED_AT TO CRIADOEM;
ALTER TABLE CPESC_PORTADORCARTAO RENAME COLUMN UPDATED_BY TO ATUALIZADOPOR;
ALTER TABLE CPESC_PORTADORCARTAO RENAME COLUMN UPDATED_AT TO ATUALIZADOEM;
ALTER TABLE CPESC_PORTADORCARTAO ADD CODIGOOTP VARCHAR2(60);
ALTER TABLE CPESC_PORTADORCARTAO ADD EXPIRACAOOTP TIMESTAMP (6);
ALTER TABLE CPESC_PORTADORCARTAO ADD ULTIMOACESSO DATE;

ALTER TABLE CPESC_PORTADORCARTAO MODIFY CRIADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE CPESC_PORTADORCARTAO MODIFY ATUALIZADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

DROP SEQUENCE CPESC_PORTADORCARTAO_ID_SEQ
*/

CREATE TABLE CPESC_PORTADORCARTAO
(
  "ID" NUMBER NOT NULL ENABLE,
  "CPF" VARCHAR2(11) NOT NULL ENABLE,
  "NOME" VARCHAR2(100) NOT NULL ENABLE,
  "DATANASCIMENTO" DATE,
  "NOMEABREVIADO" VARCHAR2(100),
  "MATRICULA" VARCHAR2(15),
  "EMAIL" VARCHAR2(150),
  "CRIADOPOR" VARCHAR2(255),
  "CRIADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "ATUALIZADOPOR" VARCHAR2(255),
  "ATUALIZADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "CODIGOOTP" VARCHAR2(60),
  "EXPIRACAOOTP" TIMESTAMP (6),
  "ULTIMOACESSO" DATE
);

ALTER TABLE CPESC_PORTADORCARTAO ADD CONSTRAINT CPESC_PORTADORCARTAO_PK PRIMARY KEY (ID);

CREATE SEQUENCE CPESC_PORTADORCARTAO_ID_SEQ START WITH 8667 INCREMENT BY 1 NOCACHE ORDER;

CREATE OR REPLACE TRIGGER  CPESC_PORTADORCARTAO_ID_TRG BEFORE
  INSERT ON CPESC_PORTADORCARTAO FOR EACH ROW WHEN (NEW.ID IS NULL) BEGIN :NEW.ID :=  CPESC_PORTADORCARTAO_ID_SEQ.NEXTVAL;
END;
--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
CREATE TABLE CPESC_PORTADORCARTAO_UNIDADEADMINISTRATIVA(
 ID NUMBER,
 PORTADORCARTAO_ID NUMBER,
 UNIDADEADMINISTRATIVA_ID NUMBER,
 MATRICULA VARCHAR2(18),
 ATIVO CHAR(1),
 CRIADOPOR VARCHAR2(255),
 CRIADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
 ATUALIZADOPOR VARCHAR2(255),
 ATUALIZADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

 ALTER TABLE CPESC_PORTADORCARTAO_UNIDADEADMINISTRATIVA ADD CONSTRAINT CPESC_PORTADORCARTAO_UNIDADEADMINISTRATIVA_PK PRIMARY KEY (ID);

 CREATE SEQUENCE CPESC_PORTADORCARTAO_UNIDADEADMINISTRATIVA_ID_SEQ START WITH 1 INCREMENT BY 1 NOCACHE ORDER;

CREATE OR REPLACE TRIGGER CPESC_PORTADORCARTAO_UNIDADEADMINISTRATIVA_ID_TRG BEFORE
  INSERT ON CPESC_PORTADORCARTAO_UNIDADEADMINISTRATIVA FOR EACH ROW  WHEN (NEW.ID IS NULL) BEGIN :NEW.ID := CPESC_PORTADORCARTAO_UNIDADEADMINISTRATIVA_ID_SEQ.NEXTVAL;
END;

INSERT INTO CPESC_PORTADORCARTAO_UNIDADEADMINISTRATIVA (PORTADORCARTAO_ID, UNIDADEADMINISTRATIVA_ID, ATIVO, MATRICULA, CRIADOEM,CRIADOPOR,ATUALIZADOEM,ATUALIZADOPOR)
  SELECT (SELECT MAX(PA.ID)FROM PORTADORCARTAO PA where P.NU_CPF = PA.NU_CPF)AS PORTADORCARTAO_ID,
     C.UNIDADEADMINISTRATIVA_ID,
     CASE FL_STATUS WHEN 1 THEN 'S' ELSE 'N' END ATIVO,
     P.NU_MATRICULA MATRICULA,
     P.CREATED_AT CRIADOEM,
     P.CREATED_BY CRIADOPOR,
     P.UPDATED_AT ATUALIZADOEM,
     P.UPDATED_BY ATUALIZADOPOR
  FROM PORTADORCARTAO P,
   (SELECT DISTINCT UNIDADEADMINISTRATIVA_ID, PORTADORCARTAO_ID FROM CARTAO) C
  WHERE P.ID = C.PORTADORCARTAO_ID;
COMMIT;

--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
/*CREATE TABLE CPESC_CONTABANCO AS SELECT * FROM CONVENIOBANCO;
ALTER TABLE CPESC_CONTABANCO DROP COLUMN NM_PREFIXOARQUIVO;
ALTER TABLE CPESC_CONTABANCO DROP COLUMN NU_VERSAO;
ALTER TABLE CPESC_CONTABANCO DROP COLUMN CD_CENTROCUSTO;
ALTER TABLE CPESC_CONTABANCO DROP COLUMN CD_UNIDADEFATURAMENTO;
ALTER TABLE CPESC_CONTABANCO DROP COLUMN FL_TIPOCARTAO;
ALTER TABLE CPESC_CONTABANCO DROP COLUMN NU_DIAVENCIMENTO;
ALTER TABLE CPESC_CONTABANCO DROP COLUMN VL_LIMITE;
ALTER TABLE CPESC_CONTABANCO DROP COLUMN CD_FONTE;
ALTER TABLE CPESC_CONTABANCO DROP COLUMN FONTERECURSO_ID;
ALTER TABLE CPESC_CONTABANCO RENAME COLUMN NU_CODIGOMCI TO CODIGOMCI;
ALTER TABLE CPESC_CONTABANCO RENAME COLUMN NU_PROCESSO TO PROCESSO;
ALTER TABLE CPESC_CONTABANCO RENAME COLUMN NU_SEQUENCIALREMESSA TO REMESSA;
ALTER TABLE CPESC_CONTABANCO RENAME COLUMN NU_AGENCIA TO AGENCIA;
ALTER TABLE CPESC_CONTABANCO RENAME COLUMN NU_AGENCIADV TO AGENCIADV;
ALTER TABLE CPESC_CONTABANCO RENAME COLUMN NU_CONTA TO CONTA;
ALTER TABLE CPESC_CONTABANCO RENAME COLUMN NU_CONTADV TO CONTADV;
ALTER TABLE CPESC_CONTABANCO RENAME COLUMN UPDATED_AT TO ATUALIZADOEM;
ALTER TABLE CPESC_CONTABANCO RENAME COLUMN UPDATED_BY TO ATUALIZADOPOR;
ALTER TABLE CPESC_CONTABANCO RENAME COLUMN CREATED_AT TO CRIADOEM;
ALTER TABLE CPESC_CONTABANCO RENAME COLUMN CREATED_BY TO CRIADOPOR;
ALTER TABLE CPESC_CONTABANCO ADD ATIVO CHAR DEFAULT 'S';

ALTER TABLE CPESC_CONTABANCO MODIFY CRIADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE CPESC_CONTABANCO MODIFY ATUALIZADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
*/

CREATE TABLE CPESC_CONTABANCO (
  "ID" NUMBER NOT NULL ENABLE,
  "UNIDADEGESTORA_ID" NUMBER NOT NULL ENABLE,
  "CODIGOMCI" VARCHAR2(10),
  "PROCESSO" NUMBER,
  "REMESSA" NUMBER,
  "AGENCIA" VARCHAR2(5),
  "AGENCIADV" VARCHAR2(2),
  "CONTA" VARCHAR2(10),
  "CONTADV" VARCHAR2(2),
  "ATUALIZADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "ATUALIZADOPOR" VARCHAR2(255),
  "CRIADOPOR" VARCHAR2(255),
  "CRIADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "ATIVO" CHAR(1) DEFAULT 'S'
);
ALTER TABLE CPESC_CONTABANCO ADD CONSTRAINT CPESC_CONTABANCO_PK PRIMARY KEY (ID);
ALTER TABLE CPESC_CONTABANCO ADD CONSTRAINT CPESC_CONTABANCO_UG_FK FOREIGN KEY (UNIDADEGESTORA_ID) REFERENCES CPESC_UNIDADEGESTORA (ID);

CREATE SEQUENCE CPESC_CONTABANCO_ID_SEQ START WITH 59 INCREMENT BY 1 NOCACHE ORDER;

CREATE OR REPLACE TRIGGER CPESC_CONTABANCO_ID_TRG BEFORE
  INSERT ON CPESC_CONTABANCO FOR EACH ROW WHEN (NEW.ID IS NULL) BEGIN :NEW.ID := CPESC_CONTABANCO_ID_SEQ.NEXTVAL;
END;

--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------

CREATE TABLE CPESC_CALCULOPAGAMENTO (
  ID NUMBER,
  DESCRICAO VARCHAR2(100),
  CONSTRAINT CPESC_CALCULOPAGAMENTO_PK PRIMARY KEY (ID)
);
INSERT INTO CPESC_CALCULOPAGAMENTO VALUES (0, 'Sem cálculo de rateio');
INSERT INTO CPESC_CALCULOPAGAMENTO VALUES (1, 'Cálculo rateio Material');
INSERT INTO CPESC_CALCULOPAGAMENTO VALUES (2, 'Cálculo rateio Serviço');
INSERT INTO CPESC_CALCULOPAGAMENTO VALUES (3, 'Cálculo rateio PRODENE');
COMMIT;

--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
/*CREATE TABLE CPESC_CENTROCUSTO AS SELECT * FROM CENTROCUSTO;
--ALTER TABLE CPESC_CENTROCUSTO DROP COLUMN UNIDADEGESTORA_ID;
ALTER TABLE CPESC_CENTROCUSTO RENAME COLUMN NM_CENTROCUSTO TO NOME;
ALTER TABLE CPESC_CENTROCUSTO RENAME COLUMN NU_CENTROCUSTO TO NUMEROBANCOCENTROCUSTO;
ALTER TABLE CPESC_CENTROCUSTO RENAME COLUMN CONVENIOBANCO_ID TO CONTABANCO_ID;
ALTER TABLE CPESC_CENTROCUSTO RENAME COLUMN UPDATED_AT TO ATUALIZADOEM;
ALTER TABLE CPESC_CENTROCUSTO RENAME COLUMN CREATED_AT TO CRIADOEM;
ALTER TABLE CPESC_CENTROCUSTO RENAME COLUMN UPDATED_BY TO ATUALIZADOPOR;
ALTER TABLE CPESC_CENTROCUSTO RENAME COLUMN CREATED_BY TO CRIADOPOR;
ALTER TABLE CPESC_CENTROCUSTO RENAME COLUMN FL_CALCULOPAGAMENTO TO CALCULOPAGAMENTO_ID;

ALTER TABLE CPESC_CENTROCUSTO MODIFY CRIADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE CPESC_CENTROCUSTO MODIFY ATUALIZADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE CPESC_CENTROCUSTO ADD ATIVO CHAR DEFAULT 'S';

*/
CREATE TABLE CPESC_CENTROCUSTO (
  "ID" NUMBER,
	"NOME" VARCHAR2(50),
	"NUMEROBANCOCENTROCUSTO" NUMBER,
	"UNIDADEGESTORA_ID" NUMBER,
	"CONTABANCO_ID" NUMBER,
	"ATUALIZADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	"CRIADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	"ATUALIZADOPOR" VARCHAR2(255),
	"CRIADOPOR" VARCHAR2(255),
	"CALCULOPAGAMENTO_ID" NUMBER(2,0),
	"ATIVO" CHAR(1) DEFAULT 'S'
);

ALTER TABLE CPESC_CENTROCUSTO ADD CONSTRAINT CPESC_CENTROCUSTO_PK PRIMARY KEY (ID);
ALTER TABLE CPESC_CENTROCUSTO ADD CONSTRAINT CPESC_CENTROCUSTO_CB_FK FOREIGN KEY (CONTABANCO_ID) REFERENCES CPESC_CONTABANCO (ID);
ALTER TABLE CPESC_CENTROCUSTO ADD CONSTRAINT CPESC_CENTROCUSTO_CALCPAG_FK FOREIGN KEY (CALCULOPAGAMENTO_ID) REFERENCES CPESC_CALCULOPAGAMENTO (ID);

CREATE SEQUENCE CPESC_CENTROCUSTO_ID_SEQ START WITH 1070 INCREMENT BY 1 NOCACHE ORDER;

CREATE OR REPLACE TRIGGER CPESC_CENTROCUSTO_ID_TRG
BEFORE INSERT ON CPESC_CENTROCUSTO FOR EACH ROW WHEN (NEW.ID IS NULL) BEGIN :NEW.ID := CPESC_CENTROCUSTO_ID_SEQ.NEXTVAL;
END;

--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
CREATE TABLE CPESC_PORTADORCARTAO_CENTROCUSTO (
  ID NUMBER,
  CENTROCUSTO_ID NUMBER,
  PORTADORCARTAO_ID NUMBER,
  PORTADORCARTAO_UNIDADEADMINISTRATIVA_ID NUMBER,
  ATIVO CHAR,
  CRIADOPOR VARCHAR2(255),
  CRIADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  ATUALIZADOPOR VARCHAR2(255),
  ATUALIZADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT CPESC_PORTADOR_CENTROCUSTO_PK PRIMARY KEY (ID)
);

CREATE SEQUENCE CPESC_PORTADORCARTAO_CENTROCUSTO_ID_SEQ START WITH 1 INCREMENT BY 1 NOCACHE ORDER;

CREATE OR REPLACE TRIGGER CPESC_PORTADORCARTAO_CENTROCUSTO_ID_TRG
BEFORE INSERT ON CPESC_PORTADORCARTAO_CENTROCUSTO FOR EACH ROW WHEN (NEW.ID IS NULL) BEGIN :NEW.ID := CPESC_PORTADORCARTAO_CENTROCUSTO_ID_SEQ.NEXTVAL;
END;

INSERT INTO CPESC_PORTADORCARTAO_CENTROCUSTO (CENTROCUSTO_ID, PORTADORCARTAO_ID, PORTADORCARTAO_UNIDADEADMINISTRATIVA_ID, ATIVO, CRIADOEM, CRIADOPOR, ATUALIZADOEM, ATUALIZADOPOR)
   SELECT C.CENTROCUSTO_ID,
     (SELECT MAX(PC.ID)FROM PORTADORCARTAO PC where P.NU_CPF = PC.NU_CPF ) AS PORTADORCARTAO_ID,
     (SELECT PA.ID
         FROM CPESC_PORTADORCARTAO_UNIDADEADMINISTRATIVA PA
         WHERE P.UNIDADEADMINISTRATIVA_ID = PA.UNIDADEADMINISTRATIVA_ID
         AND PA.PORTADORCARTAO_ID = (SELECT MAX(PC.ID)FROM PORTADORCARTAO PC where P.NU_CPF = PC.NU_CPF)) PORTADORCARTAO_UNIDADEADMINISTRATIVA_ID,
     CASE P.FL_STATUS WHEN 1 THEN 'S' ELSE 'N' END ATIVO,
     P.CREATED_AT CRIADOEM,
     P.CREATED_BY CRIADOPOR,
     P.UPDATED_AT ATUALIZADOEM,
     P.UPDATED_BY ATUALIZADOPOR
   FROM PORTADORCARTAO P,(SELECT DISTINCT CENTROCUSTO_ID, PORTADORCARTAO_ID FROM CARTAO) C
  WHERE P.ID = C.PORTADORCARTAO_ID;
COMMIT;

--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
/*CREATE TABLE CPESC_SUBELEMENTO AS SELECT U.*, CASE FL_STATUS WHEN 1 THEN 'S' ELSE 'N' END ATIVO FROM SUBELEMENTO U;
ALTER TABLE CPESC_SUBELEMENTO RENAME COLUMN CD_SUBELEMENTO TO CODIGO;
ALTER TABLE CPESC_SUBELEMENTO RENAME COLUMN NM_SUBELEMENTO TO NOME;
ALTER TABLE CPESC_SUBELEMENTO DROP COLUMN FL_STATUS;
ALTER TABLE CPESC_SUBELEMENTO RENAME COLUMN CREATED_BY TO CRIADOPOR;
ALTER TABLE CPESC_SUBELEMENTO RENAME COLUMN CREATED_AT TO CRIADOEM;
ALTER TABLE CPESC_SUBELEMENTO RENAME COLUMN UPDATED_BY TO ATUALIZADOPOR;
ALTER TABLE CPESC_SUBELEMENTO RENAME COLUMN UPDATED_AT TO ATUALIZADOEM;*/

CREATE TABLE CPESC_SUBELEMENTO(
  "ID" NUMBER,
  "CODIGO" NUMBER,
  "NOME" VARCHAR2(50),
  "CRIADOPOR" VARCHAR2(255),
  "CRIADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "ATUALIZADOPOR" VARCHAR2(255),
  "ATUALIZADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "ATIVO" CHAR(1)
  );

ALTER TABLE CPESC_SUBELEMENTO ADD CONSTRAINT CPESC_SUBELEMENTO_PK PRIMARY KEY (ID);

CREATE SEQUENCE CPESC_SUBELEMENTO_ID_SEQ START WITH 62 INCREMENT BY 1 NOCACHE ORDER;

CREATE OR REPLACE TRIGGER CPESC_SUBELEMENTO_ID_TRG
BEFORE INSERT ON CPESC_SUBELEMENTO FOR EACH ROW
   WHEN (NEW.ID IS NULL) BEGIN :NEW.ID := CPESC_SUBELEMENTO_ID_SEQ.NEXTVAL;
END;

--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
/*CREATE TABLE CPESC_SUBELEMENTO_CENTROCUSTO AS SELECT * FROM SUBELEMENTO_CENTROCUSTO;
*/

CREATE TABLE CPESC_SUBELEMENTO_CENTROCUSTO
(
  ID             NUMBER,
  CENTROCUSTO_ID NUMBER,
  SUBELEMENTO_ID NUMBER
)

CREATE SEQUENCE CPESC_SUBELEMENTO_CENTROCUSTO_ID_SEQ START WITH 3470 INCREMENT BY 1 NOCACHE ORDER;

CREATE OR REPLACE TRIGGER CPESC_SUBELEMENTO_CENTROCUSTO_ID_TRG
BEFORE INSERT ON CPESC_SUBELEMENTO_CENTROCUSTO FOR EACH ROW
   WHEN (NEW.ID IS NULL) BEGIN :NEW.ID := CPESC_SUBELEMENTO_CENTROCUSTO_ID_SEQ.NEXTVAL;
END;

--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
/*CREATE TABLE CPESC_CARTAO AS SELECT U.*, CASE FL_STATUS WHEN 1 THEN 'S' ELSE 'N' END ATIVO FROM CARTAO U;
ALTER TABLE CPESC_CARTAO DROP COLUMN PORTADORCARTAO_ID;
ALTER TABLE CPESC_CARTAO DROP COLUMN UNIDADEADMINISTRATIVA_ID;
ALTER TABLE CPESC_CARTAO DROP COLUMN CENTROCUSTO_ID;
ALTER TABLE CPESC_CARTAO DROP COLUMN FL_STATUS;
ALTER TABLE CPESC_CARTAO RENAME COLUMN NU_CARTAO TO NUMEROCARTAO;
ALTER TABLE CPESC_CARTAO RENAME COLUMN CREATED_BY TO CRIADOPOR;
ALTER TABLE CPESC_CARTAO RENAME COLUMN UPDATED_BY TO ATUALIZADOPOR;
ALTER TABLE CPESC_CARTAO RENAME COLUMN CREATED_AT TO CRIADOEM;
ALTER TABLE CPESC_CARTAO RENAME COLUMN UPDATED_AT TO ATUALIZADOEM;
ALTER TABLE CPESC_CARTAO RENAME COLUMN NU_UNIDADEFATURAMENTO TO NUCONTACARTAO;
ALTER TABLE CPESC_CARTAO ADD PORTADORCARTAOCENTROCUSTO_ID NUMBER;
ALTER TABLE CPESC_CARTAO ADD PORTADORCARTAOUNIDADEADMINISTRATIVA_ID NUMBER;

ALTER TABLE CPESC_CARTAO MODIFY CRIADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE CPESC_CARTAO MODIFY ATUALIZADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
*/
CREATE TABLE CPESC_CARTAO
(
  "ID" NUMBER,
  "NUMEROCARTAO" VARCHAR2(19) NOT NULL ENABLE,
  "CRIADOPOR" VARCHAR2(255),
  "ATUALIZADOPOR" VARCHAR2(255),
  "CRIADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "ATUALIZADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "NUCONTACARTAO" NUMBER(10,0),
  "ATIVO" CHAR(1),
  "PORTADORCARTAOCENTROCUSTO_ID" NUMBER,
  "PORTADORCARTAOUNIDADEADMINISTRATIVA_ID" NUMBER
);

ALTER TABLE CPESC_CARTAO ADD CONSTRAINT CPESC_CARTAO_PK PRIMARY KEY (ID);

CREATE SEQUENCE CPESC_CARTAO_ID_SEQ START WITH 111846 INCREMENT BY 1 NOCACHE ORDER;

CREATE OR REPLACE TRIGGER CPESC_CARTAO_ID_TRG
BEFORE INSERT ON CPESC_CARTAO FOR EACH ROW
   WHEN (NEW.ID IS NULL) BEGIN :NEW.ID := CPESC_CARTAO_ID_SEQ.NEXTVAL;
END;

MERGE INTO CPESC_CARTAO D
USING(
SELECT PC.ID AS PORTADORCARTAOCENTROCUSTO_ID, C.ID
   FROM CARTAO C, PORTADORCARTAO P,CPESC_PORTADORCARTAO_CENTROCUSTO PC
   WHERE C.PORTADORCARTAO_ID = P.ID
   AND PC.CENTROCUSTO_ID = C.CENTROCUSTO_ID
   AND PC.PORTADORCARTAO_ID = (SELECT MAX(PP.ID)FROM PORTADORCARTAO PP where PP.NU_CPF = P.NU_CPF)
)O
ON (D.ID = O.ID)
WHEN MATCHED THEN
  UPDATE SET PORTADORCARTAOCENTROCUSTO_ID = O.PORTADORCARTAOCENTROCUSTO_ID;
COMMIT;

MERGE INTO CPESC_CARTAO D
USING(
   SELECT PC.ID AS PORTADORCARTAOUNIDADEADMINISTRATIVA_ID, C.ID
FROM CARTAO C, PORTADORCARTAO PA, CPESC_PORTADORCARTAO_UNIDADEADMINISTRATIVA PC
   WHERE C.PORTADORCARTAO_ID = PA.ID
   AND PC.UNIDADEADMINISTRATIVA_ID = C.UNIDADEADMINISTRATIVA_ID
   AND PC.PORTADORCARTAO_ID = (SELECT MAX(P.ID)FROM PORTADORCARTAO P where P.NU_CPF = PA.NU_CPF)
)O
ON (D.ID = O.ID)
WHEN MATCHED THEN
  UPDATE SET PORTADORCARTAOUNIDADEADMINISTRATIVA_ID = O.PORTADORCARTAOUNIDADEADMINISTRATIVA_ID;
COMMIT;

--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
/* CREATE TABLE CPESC_LIMITECARTAO AS SELECT C.*, Z.DT_CREDITO AS DATAZERAMENTO
FROM CREDITOCARTAO C LEFT JOIN
  (
    SELECT ID AS ZERAMENTO_ID, DT_CREDITO
    FROM CREDITOCARTAO
    WHERE DE_TIPO_REGISTRO = 'ZERAMENTO_CARTAO'
  )Z
  ON Z.ZERAMENTO_ID = C.CREDITOCARTAOZERAMENTO_ID
  WHERE C.DE_TIPO_REGISTRO = 'SIGEF_PAGAMENTO';

ALTER TABLE CPESC_LIMITECARTAO DROP COLUMN CD_SITUACAO_PRESTACAO_CONTA;
ALTER TABLE CPESC_LIMITECARTAO DROP COLUMN DT_EMPENHO;
ALTER TABLE CPESC_LIMITECARTAO DROP COLUMN FL_STATUSBANCO;
ALTER TABLE CPESC_LIMITECARTAO DROP COLUMN VL_GASTO;
ALTER TABLE CPESC_LIMITECARTAO DROP COLUMN VL_DEVOLVIDO;
ALTER TABLE CPESC_LIMITECARTAO DROP COLUMN CD_UNIDADEGESTORAOB;
ALTER TABLE CPESC_LIMITECARTAO DROP COLUMN CD_GESTAOOB;
ALTER TABLE CPESC_LIMITECARTAO DROP COLUMN NU_CPFPORTADOR;
ALTER TABLE CPESC_LIMITECARTAO DROP COLUMN DE_TIPO_REGISTRO;
ALTER TABLE CPESC_LIMITECARTAO DROP COLUMN DE_TIPO_CREDITO;
ALTER TABLE CPESC_LIMITECARTAO DROP COLUMN FL_STATUS;
ALTER TABLE CPESC_LIMITECARTAO DROP COLUMN CENTROCUSTO_ID;
ALTER TABLE CPESC_LIMITECARTAO DROP COLUMN ARQUIVOBANCOZERAMENTO_ID;
ALTER TABLE CPESC_LIMITECARTAO DROP COLUMN UNIDADEADMINISTRATIVA_ID;
ALTER TABLE CPESC_LIMITECARTAO DROP COLUMN NU_SEQUENCIALREMESSA;
ALTER TABLE CPESC_LIMITECARTAO DROP COLUMN DT_PRESTACAOCONTAS;
ALTER TABLE CPESC_LIMITECARTAO DROP COLUMN FL_ESTAGIO;
ALTER TABLE CPESC_LIMITECARTAO drop COLUMN NU_PROCESSO;
ALTER TABLE CPESC_LIMITECARTAO DROP COLUMN CREDITOCARTAOZERAMENTO_ID;
ALTER TABLE CPESC_LIMITECARTAO RENAME COLUMN DT_CREDITO TO DATACREDITO;
ALTER TABLE CPESC_LIMITECARTAO RENAME COLUMN VL_CREDITO TO VALOR;
ALTER TABLE CPESC_LIMITECARTAO RENAME COLUMN NU_ORDEMBANCARIA TO NUORDEMBANCARIA;
ALTER TABLE CPESC_LIMITECARTAO RENAME COLUMN DT_VENCIMENTO TO DATAVENCIMENTO;
ALTER TABLE CPESC_LIMITECARTAO RENAME COLUMN NU_NOTA_EMPENHO TO NUNOTAEMPENHO;
ALTER TABLE CPESC_LIMITECARTAO RENAME COLUMN CD_FONTE TO CDFONTE;
ALTER TABLE CPESC_LIMITECARTAO RENAME COLUMN DE_FONTE TO DEFONTE;
ALTER TABLE CPESC_LIMITECARTAO RENAME COLUMN DE_FINALIDADE TO DEFINALIDADENE;
ALTER TABLE CPESC_LIMITECARTAO RENAME COLUMN NU_NOTA_LANCAMENTO TO NUNOTALANCAMENTO;
ALTER TABLE CPESC_LIMITECARTAO RENAME COLUMN NU_DOCUMENTO TO NUDOCUMENTOPRESTADORES;
ALTER TABLE CPESC_LIMITECARTAO RENAME COLUMN NU_PREPARACAOPAGAMENTO TO NUPREPARACAOPAGAMENTO;
ALTER TABLE CPESC_LIMITECARTAO RENAME COLUMN CD_UNIDADEGESTORA TO CDUNIDADEGESTORA;
ALTER TABLE CPESC_LIMITECARTAO RENAME COLUMN CD_GESTAO TO CDGESTAO;
ALTER TABLE CPESC_LIMITECARTAO RENAME COLUMN DT_LIMITEMOVIMENTACAO TO DATALIMITEMOVIMENTACAO;
ALTER TABLE CPESC_LIMITECARTAO RENAME COLUMN DT_INICIOMOVIMENTACAO TO DATAINICIOMOVIMENTACAO;
ALTER TABLE CPESC_LIMITECARTAO RENAME COLUMN CREATED_AT TO CRIADOEM;
ALTER TABLE CPESC_LIMITECARTAO RENAME COLUMN UPDATED_AT TO ATUALIZADOEM;
ALTER TABLE CPESC_LIMITECARTAO RENAME COLUMN CREATED_BY TO CRIADOPOR;
ALTER TABLE CPESC_LIMITECARTAO RENAME COLUMN UPDATED_BY TO ATUALIZADOPOR;

ALTER TABLE CPESC_LIMITECARTAO MODIFY CRIADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE CPESC_LIMITECARTAO MODIFY ATUALIZADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
*/

CREATE TABLE CPESC_LIMITECARTAO (
  "ID" NUMBER NOT NULL ENABLE,
  "DATACREDITO" DATE NOT NULL ENABLE,
  "VALOR" NUMBER(15,2),
  "NUORDEMBANCARIA" VARCHAR2(12),
  "DATAVENCIMENTO" DATE,
  "NUNOTAEMPENHO" VARCHAR2(12),
  "CDFONTE" VARCHAR2(15),
  "DEFONTE" VARCHAR2(150),
  "DEFINALIDADENE" VARCHAR2(2000),
  "NUNOTALANCAMENTO" VARCHAR2(12),
  "CARTAO_ID" NUMBER,
  "NUDOCUMENTOPRESTADORES" VARCHAR2(16),
  "NUPREPARACAOPAGAMENTO" VARCHAR2(12),
  "CDUNIDADEGESTORA" NUMBER(6,0),
  "CDGESTAO" NUMBER(5,0),
  "ARQUIVOBANCO_ID" NUMBER,
  "SUBELEMENTO_ID" NUMBER,
  "DATALIMITEMOVIMENTACAO" DATE,
  "DATAINICIOMOVIMENTACAO" DATE,
  "CRIADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "ATUALIZADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "CRIADOPOR" VARCHAR2(100),
  "ATUALIZADOPOR" VARCHAR2(100),
  "DATAZERAMENTO" DATE
  );
ALTER TABLE CPESC_LIMITECARTAO ADD CONSTRAINT CPESC_LIMITECARTAO_PK PRIMARY KEY (ID);

CREATE SEQUENCE CPESC_LIMITECARTAO_ID_SEQ START WITH 173378 INCREMENT BY 1 NOCACHE ORDER;

CREATE OR REPLACE TRIGGER CPESC_LIMITECARTAO_ID_TRG BEFORE
  INSERT ON CPESC_LIMITECARTAO FOR EACH ROW WHEN (NEW.ID IS NULL) BEGIN :NEW.ID := CPESC_LIMITECARTAO_ID_SEQ.NEXTVAL;
END;

ALTER TABLE CPESC_LIMITECARTAO ADD CONSTRAINT CPESC_LIMITECARTAO_SUBELEMENTO_FK FOREIGN KEY (SUBELEMENTO_ID) REFERENCES CPESC_SUBELEMENTO (ID);
ALTER TABLE CPESC_LIMITECARTAO ADD CONSTRAINT CPESC_LIMITECARTAO_CARTAO_FK FOREIGN KEY (CARTAO_ID) REFERENCES CPESC_CARTAO (ID);


/*MERGE INTO CPESC_LIMITECARTAO D
USING(
  WITH TT_ZERAMENTO AS (
    SELECT
       ID AS ZERAMENTO_ID,
       DT_CREDITO
    FROM CREDITOCARTAO
    WHERE DE_TIPO_REGISTRO = 'ZERAMENTO_CARTAO'
  )
  SELECT Z.DT_CREDITO AS DT_ZERAMENTO, C.ID
  FROM CREDITOCARTAO C
  JOIN TT_ZERAMENTO Z ON Z.ZERAMENTO_ID = C.CREDITOCARTAOZERAMENTO_ID
  WHERE C.DE_TIPO_REGISTRO = 'SIGEF_PAGAMENTO'
   )C
ON ( D.ID = C.ID)
WHEN MATCHED THEN
  UPDATE SET DTZERAMENTO = DT_ZERAMENTO;
COMMIT;*/

--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
/*CREATE TABLE CPESC_MOVIMENTACAO AS SELECT * FROM MOVIMENTACAO;
ALTER TABLE CPESC_MOVIMENTACAO DROP COLUMN DT_JULIANA;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN NU_CARTAO TO NUMEROCARTAO;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN DT_RECEBIMENTO TO DATARECEBIMENTO;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN CD_REFERENCIA_VISA TO CDREFERENCIAVISA;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN NU_SEQUENCIAL_BB TO NUSEQUENCIALBB;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN NU_SEQUENCIAL_VISA TO NUSEQUENCIALVISA;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN NM_ESTABELECIMENTO TO NOMEESTABELECIMENTO;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN NM_CIDADE_ESTABELECIMENTO TO CIDADEESTABELECIMENTO;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN NM_UF_ESTABELECIMENTO TO UFESTABELECIMENTO;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN NU_CEP_ESTABELECIMENTO TO CEPESTABELECIMENTO;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN VL_TRANSACAO_MOEDA TO VALORTRANSACAOMOEDA;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN VL_TRANSACAO_REAL TO VALORTRANSACAOREAL;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN CD_MOEDA_ORIGINAL TO CODIGOMOEDAORIGINAL;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN CD_RAMO_ATIVIDADE TO CODIGORAMOATIVIDADE;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN CD_TIPO_TRANSACAO TO CODIGOTIPOTRANSACAO;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN DT_TRANSACAO TO DATATRANSACAO;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN CD_MOEDA_TRANSACAO TO CODIGOMOEDATRANSACAO;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN NM_MOEDA_ORIGINAL TO NOMEMOEDAORIGINAL;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN NU_MATRICULA_PORTADOR TO MATRICULAPORTADOR;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN NM_LOCALIZACAO TO LOCALIZACAO;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN VL_TRANSACAO_DOLAR TO VALORTRANSACAODOLAR;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN CD_TRANSACAO_BB TO CODIGOTRANSACAOBB;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN FL_INDICAR_DC TO INDICATIVODEBITOCREDITO;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN DE_DESCRICAO_TRANSACAO TO DESCRICAOTRANSACAO;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN NU_CENTRO_CUSTO TO NUMEROBANCOCENTROCUSTO;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN NU_UNIDADE_FATURAMENTO TO NUCONTACARTAO;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN NM_CENTRO_CUSTO TO NOMECENTROCUSTO;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN NM_UNIDADE_FATURAMENTO TO NOMECONTACARTAO;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN NM_PORTADOR TO NOMEPORTADOR;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN NU_CPF_PORTADOR TO CPFPORTADOR;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN CD_AUTORIZACAO TO CODIGOAUTORIZACAO;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN NM_RAMO_ATIVIDADE TO RAMOATIVIDADE;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN NU_CNPJ_ESTABELECIMENTO TO CNPJESTABELECIMENTO;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN HR_TRANSACAO TO HORATRANSACAO;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN ARQUIVOCARTAO_ID TO ARQUIVORETORNO_ID;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN FL_TIPOMOVIMENTACAO TO TIPOMOVIMENTACAO;
ALTER TABLE CPESC_MOVIMENTACAO RENAME COLUMN CREDITOCARTAO_ID TO LIMITECARTAO_ID;
ALTER TABLE CPESC_MOVIMENTACAO ADD CRIADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE CPESC_MOVIMENTACAO ADD ATUALIZADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

*/

CREATE TABLE CPESC_MOVIMENTACAO
(
  "ID" NUMBER NOT NULL ENABLE,
  "NUMEROCARTAO" VARCHAR2(19),
  "DATARECEBIMENTO" DATE,
  "CDREFERENCIAVISA" VARCHAR2(24),
  "NUSEQUENCIALBB" NUMBER(10,0),
  "NUSEQUENCIALVISA" NUMBER(6,0),
  "NOMEESTABELECIMENTO" VARCHAR2(26),
  "CIDADEESTABELECIMENTO" VARCHAR2(14),
  "UFESTABELECIMENTO" VARCHAR2(4),
  "CEPESTABELECIMENTO" VARCHAR2(10),
  "VALORTRANSACAOMOEDA" NUMBER(16,2),
  "VALORTRANSACAOREAL" NUMBER(16,2),
  "CODIGOMOEDAORIGINAL" NUMBER(5,0),
  "CODIGORAMOATIVIDADE" NUMBER(5,0),
  "CODIGOTIPOTRANSACAO" VARCHAR2(2),
  "DATATRANSACAO" DATE,
  "CODIGOMOEDATRANSACAO" NUMBER(5,0),
  "NOMEMOEDAORIGINAL" VARCHAR2(20),
  "MATRICULAPORTADOR" VARCHAR2(8),
  "LOCALIZACAO" VARCHAR2(9),
  "VALORTRANSACAODOLAR" NUMBER(16,2),
  "CODIGOTRANSACAOBB" NUMBER(6,0),
  "FLINDICARDC" VARCHAR2(2),
  "DESCRICAOTRANSACAO" VARCHAR2(50),
  "NUMEROCENTROCUSTO" VARCHAR2(10),
  "NUCONTACARTAO" VARCHAR2(10),
  "NOMECENTROCUSTO" VARCHAR2(20),
  "NOMECONTACARTAO" VARCHAR2(20),
  "NOMEPORTADOR" VARCHAR2(20),
  "CPFPORTADOR" VARCHAR2(11),
  "CODIGOAUTORIZACAO" VARCHAR2(10),
  "RAMOATIVIDADE" VARCHAR2(29),
  "CNPJESTABELECIMENTO" VARCHAR2(14),
  "HORATRANSACAO" DATE,
  "ARQUIVORETORNO_ID" NUMBER NOT NULL ENABLE,
  "TIPOMOVIMENTACAO" NUMBER,
  "UNIDADEADMINISTRATIVA_ID" NUMBER(10,0),
  "LIMITECARTAO_ID" NUMBER,
  "CRIADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "ATUALIZADOEM" DEFAULT CURRENT_TIMESTAMP
)

ALTER TABLE CPESC_MOVIMENTACAO ADD CONSTRAINT CPESC_MOVIMENTACAO_PK PRIMARY KEY (ID);

ALTER TABLE CPESC_MOVIMENTACAO ADD CONSTRAINT CPESC_MOVIMENTACAO_LIMCARTAO_FK FOREIGN KEY (LIMITECARTAO_ID) REFERENCES CPESC_LIMITECARTAO (ID);

CREATE SEQUENCE CPESC_MOVIMENTACAO_ID_SEQ START WITH 527140 INCREMENT BY 1 NOCACHE ORDER;

CREATE OR REPLACE TRIGGER CPESC_MOVIMENTACAO_ID_TRG BEFORE
  INSERT ON CPESC_MOVIMENTACAO FOR EACH ROW WHEN (NEW.ID IS NULL) BEGIN :NEW.ID := CPESC_MOVIMENTACAO_ID_SEQ.NEXTVAL;
END;


--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
/*CREATE TABLE CPESC_TIPOESTABELECIMENTO AS SELECT T.*, CASE FL_HABILITADO WHEN 1 THEN 'S' ELSE 'N' END HABILITADO FROM TIPOESTABELECIMENTO T;
ALTER TABLE CPESC_TIPOESTABELECIMENTO RENAME COLUMN NM_TIPOESTABELECIMENTO TO NOME;
ALTER TABLE CPESC_TIPOESTABELECIMENTO DROP COLUMN FL_HABILITADO;
*/
CREATE TABLE CPESC_TIPOESTABELECIMENTO  (
  ID NUMBER NOT NULL ENABLE,
	NOME VARCHAR2(50),
	HABILITADO CHAR
   );

ALTER TABLE CPESC_TIPOESTABELECIMENTO ADD CONSTRAINT CPESC_TIPOESTABELECIMENTO_PK PRIMARY KEY(ID);

CREATE SEQUENCE CPESC_TIPOESTABELECIMENTO_ID_SEQ START WITH 100 INCREMENT BY 1 NOCACHE ORDER;

CREATE OR REPLACE TRIGGER CPESC_TIPOESTABELECIMENTO_ID_TRG BEFORE
  INSERT ON CPESC_TIPOESTABELECIMENTO FOR EACH ROW WHEN (NEW.ID IS NULL) BEGIN :NEW.ID := TIPOESTABELECIMENTO_ID_SEQ.NEXTVAL;
END;

--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
/*CREATE TABLE CPESC_TIPOESTABELECIMENTO_CENTROCUSTO AS SELECT * FROM TIPOESTABELECIMENTO_UG;
ALTER TABLE CPESC_TIPOESTABELECIMENTO_CENTROCUSTO RENAME COLUMN VL_LIMITEDIARIO TO VALORLIMITEDIARIO;
ALTER TABLE CPESC_TIPOESTABELECIMENTO_CENTROCUSTO RENAME COLUMN VL_LIMITESEMANAL TO VALORLIMITESEMANAL;
ALTER TABLE CPESC_TIPOESTABELECIMENTO_CENTROCUSTO RENAME COLUMN VL_LIMITEMENSAL TO VALORLIMITEMENSAL;
ALTER TABLE CPESC_TIPOESTABELECIMENTO_CENTROCUSTO RENAME COLUMN CREATED_AT TO CRIADOEM;
ALTER TABLE CPESC_TIPOESTABELECIMENTO_CENTROCUSTO RENAME COLUMN UPDATED_AT TO ATUALIZADOEM;
ALTER TABLE CPESC_TIPOESTABELECIMENTO_CENTROCUSTO RENAME COLUMN CREATED_BY TO CRIADOPOR;
ALTER TABLE CPESC_TIPOESTABELECIMENTO_CENTROCUSTO RENAME COLUMN UPDATED_BY TO ATUALIZADOPOR;
ALTER TABLE CPESC_TIPOESTABELECIMENTO_CENTROCUSTO DROP COLUMN UNIDADEGESTORA_ID;

ALTER TABLE CPESC_TIPOESTABELECIMENTO_CENTROCUSTO MODIFY CRIADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE CPESC_TIPOESTABELECIMENTO_CENTROCUSTO MODIFY ATUALIZADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
*/
CREATE TABLE CPESC_TIPOESTABELECIMENTO_CENTROCUSTO
(
  "ID" NUMBER NOT NULL ENABLE,
	"TIPOESTABELECIMENTO_ID" NUMBER NOT NULL ENABLE,
	"VALORLIMITEDIARIO" NUMBER(15,2),
	"VALORLIMITESEMANAL" NUMBER(15,2),
	"VALORLIMITEMENSAL" NUMBER(15,2),
	"CENTROCUSTO_ID" NUMBER,
	"CRIADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	"ATUALIZADOEM" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
	"CRIADOPOR" VARCHAR2(255),
	"ATUALIZADOPOR" VARCHAR2(255)
);

ALTER TABLE CPESC_TIPOESTABELECIMENTO_CENTROCUSTO ADD CONSTRAINT CPESC_TIPOESTABELECIMENTO_CENTROCUSTO_PK PRIMARY KEY (ID);

ALTER TABLE CPESC_TIPOESTABELECIMENTO_CENTROCUSTO ADD CONSTRAINT CPESC_TIPOESTABELECIMENTO_CENTROCUSTO_CENTROCUSTO_FK FOREIGN KEY (CENTROCUSTO_ID) REFERENCES CPESC_CENTROCUSTO (ID);

CREATE SEQUENCE CPESC_TIPOESTABELECIMENTO_CENTROCUSTO_ID_SEQ START WITH 1311 INCREMENT BY 1 NOCACHE ORDER;

CREATE OR REPLACE TRIGGER CPESC_TIPOESTABELECIMENTO_CENTROCUSTO_ID_TRG BEFORE
  INSERT ON CPESC_TIPOESTABELECIMENTO_CENTROCUSTO FOR EACH ROW WHEN (NEW.ID IS NULL) BEGIN :NEW.ID :=CPESC_TIPOESTABELECIMENTO_CENTROCUSTO_ID_SEQ.NEXTVAL;
END;


--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
CREATE TABLE CPESC_PRESTACAOCONTAS (
  ID NUMBER,
  LIMITECARTAO_ID NUMBER,
  VALORGASTO NUMBER(15,2),
  IP VARCHAR2(200),
  SITUACAO CHAR(1),
  ANOSGPE VARCHAR2(4),
  ORGAOSGPE VARCHAR2(50),
  PROCESSOSGPE VARCHAR2(50),
  CRIADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  CRIADOPOR VARCHAR2(255),
  ATUALIZADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  ATUALIZADOPOR VARCHAR2(255),
  MOTIVOCANCELAMENTO VARCHAR2(200),
  CANCELADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  CANCELADOPOR VARCHAR2(255),
  IPCANCELAMENTO VARCHAR2(200)
);
 ALTER TABLE CPESC_PRESTACAOCONTAS ADD CONSTRAINT CPESC_PRESTACAOCONTAS_PK PRIMARY KEY (ID);
comment on column CPESC_PRESTACAOCONTAS.ip is 'IP do usuário';
comment on column CPESC_PRESTACAOCONTAS.situacao is 'R (Realizada) C (Cancelada)';
comment on column CPESC_PRESTACAOCONTAS.ipcancelamento is 'IP do usuário que cancelou';


 CREATE SEQUENCE CPESC_PRESTACAOCONTAS_ID_SEQ START WITH 1 INCREMENT BY 1 NOCACHE ORDER;

 ALTER TABLE CPESC_PRESTACAOCONTAS ADD CONSTRAINT CPESC_PRESTACAOCONTAS_LIMITECARTAO_FK FOREIGN KEY (LIMITECARTAO_ID) REFERENCES CPESC_LIMITECARTAO (ID);


CREATE OR REPLACE TRIGGER CPESC_PRESTACAOCONTAS_ID_TRG BEFORE
  INSERT ON CPESC_PRESTACAOCONTAS FOR EACH ROW  WHEN (NEW.ID IS NULL) BEGIN :NEW.ID := CPESC_PRESTACAOCONTAS_ID_SEQ.NEXTVAL;
END;

INSERT INTO CPESC_PRESTACAOCONTAS (LIMITECARTAO_ID, VALORGASTO, CRIADOEM, CRIADOPOR, ATUALIZADOEM, ATUALIZADOPOR, SITUACAO, ANOSGPE, ORGAOSGPE, PROCESSOSGPE)
 SELECT ID AS LIMITECARTAO_ID, VL_GASTO AS VALORGASTO,
   C.DT_PRESTACAOCONTAS AS CRIADOEM, C.CREATED_BY CRIADOPOR,
   C.DT_PRESTACAOCONTAS ATUALIZADOEM, C.UPDATED_BY ATUALIZADOPOR,
   CASE CD_SITUACAO_PRESTACAO_CONTA WHEN 1 THEN 'R' END AS SITUACAO,
   SUBSTR(C.NU_PROCESSO, LENGTH(C.NU_PROCESSO) - 3) as ANOSGPE,
   REGEXP_REPLACE(REGEXP_SUBSTR(C.NU_PROCESSO, '^[A-Za-z\/\.\-]+'),'[\/\.\-]', '') as ORGAOSGPE,
   REGEXP_REPLACE(
                 SUBSTR(
                   C.NU_PROCESSO,
                   LENGTH(REGEXP_SUBSTR(C.NU_PROCESSO, '^[A-Za-z\/\.\-]+')) + 1,
                   LENGTH(C.NU_PROCESSO) - LENGTH(REGEXP_SUBSTR(C.NU_PROCESSO, '^[A-Za-z\/\.\-]+')) - 4
                 ),
                 '[\/\.\-]', ''
               ) as PROCESSOSGPE
 FROM CREDITOCARTAO C WHERE DT_PRESTACAOCONTAS IS NOT NULL AND CD_SITUACAO_PRESTACAO_CONTA =1;
 COMMIT;

--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
 CREATE TABLE CPESC_TIPODOCUMENTOFISCAL (
  ID NUMBER,
  DESCRICAO VARCHAR2(100),
  TIPO VARCHAR2(10)
);

 ALTER TABLE CPESC_TIPODOCUMENTOFISCAL ADD CONSTRAINT CPESC_TIPODOCUMENTOFISCAL_PK PRIMARY KEY (ID);

INSERT INTO CPESC_TIPODOCUMENTOFISCAL (ID, DESCRICAO, TIPO) VALUES (1,'NOTA FISCAL ELETRÔNICA','IMPORTAÇÃO');
INSERT INTO CPESC_TIPODOCUMENTOFISCAL (ID, DESCRICAO, TIPO) VALUES (2,'NOTA FISCAL ELETRÔNICA','MANUAL');
INSERT INTO CPESC_TIPODOCUMENTOFISCAL (ID, DESCRICAO, TIPO) VALUES (3,'NOTA FISCAL SERVIÇO','MANUAL');
INSERT INTO CPESC_TIPODOCUMENTOFISCAL (ID, DESCRICAO, TIPO) VALUES (4,'CUPOM FISCAL','MANUAL');
INSERT INTO CPESC_TIPODOCUMENTOFISCAL (ID, DESCRICAO, TIPO) VALUES (5,'REVESÃO','MANUAL');

COMMIT;
--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
 CREATE TABLE CPESC_DOCUMENTOFISCAL (
  ID NUMBER,
  MOVIMENTACAO_ID NUMBER,
  TIPODOCUMENTOFISCAL_ID NUMBER,
  NUMERO NUMBER,
  SERIE NUMBER,
  CNPJ VARCHAR2(14),
  CHAVE VARCHAR2(44),
  VALOR NUMBER(15,2),
  DESCONTO NUMBER(15,2),
  COFINS NUMBER(15,2),
  ISS NUMBER(15,2),
  PIS NUMBER(15,2),
  INSS NUMBER(15,2),
  IR NUMBER(15,2),
  DATAEMISSAO DATE,
  CODIGOSIGEF NUMBER,
  CRIADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  CRIADOPOR VARCHAR2(255),
  ATUALIZADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  ATUALIZADOPOR VARCHAR2(255)
);

COMMENT ON COLUMN CPESC_DOCUMENTOFISCAL.CODIGOSIGEF IS 'Campo nunfeid no SIGEF';

 ALTER TABLE CPESC_DOCUMENTOFISCAL ADD CONSTRAINT CPESC_DOCUMENTOFISCAL_PK PRIMARY KEY (ID);

 CREATE SEQUENCE CPESC_DOCUMENTOFISCAL_ID_SEQ START WITH 1 INCREMENT BY 1 NOCACHE ORDER;

CREATE OR REPLACE TRIGGER CPESC_DOCUMENTOFISCAL_ID_TRG BEFORE
  INSERT ON CPESC_DOCUMENTOFISCAL FOR EACH ROW  WHEN (NEW.ID IS NULL) BEGIN :NEW.ID := CPESC_DOCUMENTOFISCAL_ID_SEQ.NEXTVAL;
END;

--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
 CREATE TABLE CPESC_DOCUMENTOFISCALITEM (
  ID NUMBER,
  DOCUMENTOFISCAL_ID NUMBER,
  NCM VARCHAR(20),
  DESCRICAO VARCHAR2(200),
  QUANTIDADE NUMBER,
  UNIDADE VARCHAR2(20),
  VALORUNITARIO NUMBER(15,2),
  VALOR NUMBER(15,2),
  CRIADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  CRIADOPOR VARCHAR2(255),
  ATUALIZADOEM TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  ATUALIZADOPOR VARCHAR2(255)
);
COMMENT ON COLUMN CPESC_DOCUMENTOFISCALITEM.QUANTIDADE IS 'Campo qtcomercial no SIGEF';
COMMENT ON COLUMN CPESC_DOCUMENTOFISCALITEM.UNIDADE IS 'Campo deundicomercial no SIGEF';



 ALTER TABLE CPESC_DOCUMENTOFISCALITEM ADD CONSTRAINT CPESC_DOCUMENTOFISCALITEM_PK PRIMARY KEY (ID);

 CREATE SEQUENCE CPESC_DOCUMENTOFISCALITEM_ID_SEQ START WITH 1 INCREMENT BY 1 NOCACHE ORDER;

CREATE OR REPLACE TRIGGER CPESC_DOCUMENTOFISCALITEM_ID_TRG BEFORE
  INSERT ON CPESC_DOCUMENTOFISCALITEM FOR EACH ROW  WHEN (NEW.ID IS NULL) BEGIN :NEW.ID := CPESC_DOCUMENTOFISCALITEM_ID_SEQ.NEXTVAL;
END;
--------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------
CREATE TABLE "APPCPESC"."CPESC_ITEMFISCAL"
   ( "ID" NUMBER,
"NCM" VARCHAR2(8),
"DESCRICAO" VARCHAR2(4000),
"TIPO" CHAR(1),
"PESQUISA" VARCHAR2(4000),
CONSTRAINT "PK_ITEM_FISCAL" PRIMARY KEY ("ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "TBS_PORTAIS_SIGEF"  ENABLE
   ) SEGMENT CREATION IMMEDIATE
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "TBS_PORTAIS_SIGEF" ;
  CREATE INDEX "APPCPESC"."IDX_NCM" ON "APPCPESC"."CPESC_ITEMFISCAL" ("NCM")
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "TBS_PORTAIS_SIGEF" ;
  CREATE INDEX "APPCPESC"."IDX_DESCRICAO" ON "APPCPESC"."CPESC_ITEMFISCAL" ("DESCRICAO")
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "TBS_PORTAIS_SIGEF" ;
  CREATE UNIQUE INDEX "APPCPESC"."PK_ITEM_FISCAL" ON "APPCPESC"."CPESC_ITEMFISCAL" ("ID")
  PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "TBS_PORTAIS_SIGEF" ;
  ALTER TABLE "APPCPESC"."CPESC_ITEMFISCAL" ADD CONSTRAINT "PK_ITEM_FISCAL" PRIMARY KEY ("ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "TBS_PORTAIS_SIGEF"  ENABLE;
   COMMENT ON COLUMN "APPCPESC"."CPESC_ITEMFISCAL"."NCM" IS 'NCM - Nomenclatura Comum Mercosul ou Codigo do serviço segundo lei LCP116';
   COMMENT ON COLUMN "APPCPESC"."CPESC_ITEMFISCAL"."DESCRICAO" IS 'Descrição do material ou serviço';
   COMMENT ON COLUMN "APPCPESC"."CPESC_ITEMFISCAL"."TIPO" IS '''S''-Servico ''M''-Material';


-----------------------------------------------------------------------------------------
--drop VIEW VPORTADORCARTAO_CREDITO;
CREATE OR REPLACE VIEW VPORTADORCARTAO_CREDITO AS
SELECT LC.ID AS CPESC_LIMITECARTAO_ID,
       LC.VALOR,
       LC.DATACREDITO,
       LC.DATAVENCIMENTO,
       LC.NUNOTAEMPENHO,
       LC.NUNOTALANCAMENTO,
       LC.NUPREPARACAOPAGAMENTO,
       LC.NUORDEMBANCARIA,
       LC.CDUNIDADEGESTORA,
       LC.CDFONTE,
       LC.DEFONTE,
       C.NUMEROCARTAO,
       C.NUCONTACARTAO,
       PUA.PORTADORCARTAO_ID,
       LC.SUBELEMENTO_ID,
       PUA.UNIDADEADMINISTRATIVA_ID,
       UA.UNIDADEGESTORA_ID,
       PC.ID AS PRESTACAOCONTAS_ID
  FROM CPESC_LIMITECARTAO LC
 INNER JOIN CPESC_CARTAO C
    ON LC.CARTAO_ID = C.ID
 INNER JOIN CPESC_PORTADORCARTAO_UNIDADEADMINISTRATIVA PUA
    ON PUA.ID = C.PORTADORCARTAOCENTROCUSTO_ID
 INNER JOIN CPESC_UNIDADEADMINISTRATIVA UA
    ON UA.ID = PUA.UNIDADEADMINISTRATIVA_ID
 INNER JOIN CPESC_PRESTACAOCONTAS PC
    ON PC.LIMITECARTAO_ID = LC.ID;
;

CREATE TABLE CPESC_REVERSAOMOVIMENTACAO (
    ID                NUMBER GENERATED BY DEFAULT ON NULL AS IDENTITY PRIMARY KEY,
    REVERSAO_ID       NUMBER NOT NULL,
    MOVIMENTACAO_ID   NUMBER NOT NULL,
    REVERTIDOEM       TIMESTAMP,
    CRIADOPOR         VARCHAR2(255 CHAR) NOT NULL,
    CRIADOEM          TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    ATUALIZADOPOR     VARCHAR2(255 CHAR) NOT NULL,
    ATUALIZADOEM      TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT FK_REVERSAOMOVIMENTACAO_REVERSAO
        FOREIGN KEY (REVERSAO_ID) REFERENCES CPESC_MOVIMENTACAO(ID),
    CONSTRAINT FK_REVERSAOMOVIMENTACAO_MOVIMENTACAO
        FOREIGN KEY (MOVIMENTACAO_ID) REFERENCES CPESC_MOVIMENTACAO(ID)
);
