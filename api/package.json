{"name": "cpesc-api", "private": true, "version": "2.0.0", "type": "module", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "npm run start:debug", "start:dev": "nest start --watch --preserveWatchOutput", "start:debug": "nest start --debug --watch --preserveWatchOutput", "start:prod": "node dist/main", "test": "NODE_OPTIONS=\"--max-old-space-size=8192 --experimental-vm-modules\" jest", "test:watch": "npm test -- --watch", "test:cov": "npm test -- --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "*", "@nestjs/platform-express": "^11.1.3", "@nestjs/typeorm": "^11.0.0", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "cookie-parser": "^1.4.7", "lodash": "^4.17.21", "nodemailer": "^7.0.5", "oracledb": "^6.8.0", "otp-generator": "^4.0.1", "pg": "^8.16.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "typeorm": "^0.3.25", "zod": "^4.0.5"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.3", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.9", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.20", "@types/node": "^22.15.21", "@types/nodemailer": "^6.4.17", "@types/otp-generator": "^4.0.2", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "jest": "^30.0.4", "prettier": "^3.6.2", "source-map-support": "^0.5.21", "supertest": "^7.1.3", "ts-jest": "^29.4.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}