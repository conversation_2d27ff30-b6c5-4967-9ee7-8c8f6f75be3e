import type { Provider, Type } from "@nestjs/common";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { createRepositoryMock } from "./mock-repository.js";

export class TestModuleBuilder {
  private definicaoModulo = {
    controllers: [] as Type[],
    providers: [] as Provider[],
    imports: [] as Type[],
  };

  private entidadesMock: Type[] = [];

  addControllers(...controllers: Type[]): this {
    this.definicaoModulo.controllers.push(...controllers);
    return this;
  }

  addProviders(...providers: Provider[]): this {
    this.definicaoModulo.providers.push(...providers);
    return this;
  }

  addImports(...imports: Type[]): this {
    this.definicaoModulo.imports.push(...imports);
    return this;
  }

  mockEntities(...entities: Type[]): this {
    this.entidadesMock.push(...entities);
    return this;
  }

  async build(): Promise<TestingModule> {
    const repositoryMocks = this.entidadesMock.map(entity => ({
      provide: getRepositoryToken(entity),
      useFactory: createRepositoryMock,
    }));
    this.definicaoModulo.providers.push(...repositoryMocks);
    const builderModulo = Test.createTestingModule(this.definicaoModulo);

    for (const entity of this.entidadesMock) {
      builderModulo.overrideProvider(getRepositoryToken(entity)).useFactory({ factory: createRepositoryMock });
    }

    return builderModulo.compile();
  }
}
