import { ClassSerializerInterceptor, Controller, Get, Param, Req, UseGuards, UseInterceptors } from "@nestjs/common";
import { endpoints, type RespostaRota } from "cpesc-shared/out/endpoints/main.js";
import { AuthGuard, RequisicaoAutenticada } from "../auth/auth.guard.js";
import { PortadorService } from "./portador.service.js";

@Controller("portador")
@UseGuards(AuthGuard)
export class PortadorController {
  constructor(private readonly portadorService: PortadorService) {}

  //Para fazer funcionar o decorator @Exclude() do campo na entity portador é necessário utilizar a linha abaixo
  @UseInterceptors(ClassSerializerInterceptor)
  @Get()
  async findAll() {
    return this.portadorService.getPortadores();
  }

  @UseInterceptors(ClassSerializerInterceptor)
  @Get("/:id")
  async findPortadorAtivoPorId(@Param("id") id: string) {
    const portador = await this.portadorService.getPortadorAtivoPorId(+id);

    if (portador === null) {
      return {};
    }

    return portador;
  }

  // @UseInterceptors(ClassSerializerInterceptor)
  // // temp:
  // @Get("unidade-gestora-ativo/:id")
  // async findPortadorAtivoPorUnidadeGestora(
  //   @Req() request: RequisicaoAutenticada, @Param("id") id: string) {
  //   return this.portadorService.getPortadorAtivoPorUnidadeGestora(request.usuario, +id);
  // }

  @UseInterceptors(ClassSerializerInterceptor)
  @Get("unidade-gestora/:id")
  async findPortadorPorUnidadeGestora(
    @Req() request: RequisicaoAutenticada,
    @Param("id") id: string,
  ): RespostaRota<typeof endpoints.buscarPortadoresPorUG> {
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    return this.portadorService.getPortadorPorUnidadeGestora(request.usuario!, +id);
  }
}
