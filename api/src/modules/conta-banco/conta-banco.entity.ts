import { ContaBanco } from "cpesc-shared";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryGeneratedColumn } from "typeorm";
import type { ApiUnidadeGestora } from "../unidade-gestora/unidade-gestora.entity.js";

@Entity("CPESC_CONTABANCO")
export class ApiContaBanco implements ContaBanco {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "CODIGOMCI" })
  codigoMci: string;

  @Column({ name: "PROCESSO" })
  processo: number;

  @Column({ name: "AGENCI<PERSON>" })
  agencia: string;

  @Column({ name: "AGENCIADV" })
  digitoAgencia: string;

  @Column({ name: "CONTA" })
  conta: string;

  @Column({ name: "CONTADV" })
  digitoConta: string;

  @Column({ name: "ATUALIZADOPOR" })
  atualizadoPor: string;

  @Column({ name: "CRIADOPOR" })
  criadoPor: string;

  @Column({ name: "ATUALIZADOEM" })
  atualizadoEm: Date;

  @Column({ name: "CRIADOEM" })
  criadoEm: Date;

  @OneToOne("ApiUnidadeGestora", (ug: ApiUnidadeGestora) => ug.id)
  @JoinColumn({ name: "UNIDADEGESTORA_ID" })
  unidadeGestora: ApiUnidadeGestora;
}
