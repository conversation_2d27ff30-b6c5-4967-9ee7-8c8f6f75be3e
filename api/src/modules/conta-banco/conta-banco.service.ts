import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { ApiContaBanco } from "./conta-banco.entity.js";

@Injectable()
export class ContaBancoService {
  constructor(@InjectRepository(ApiContaBanco) private contaBancoRepository: Repository<ApiContaBanco>) {}

  async getContaBancos(): Promise<ApiContaBanco[]> {
    return this.contaBancoRepository.find();
  }

  async getContaBancoPorId(id: number): Promise<ApiContaBanco | null> {
    return this.contaBancoRepository.findOneBy({ id });
  }

  async setContaBanco(convenioBanco: ApiContaBanco): Promise<void> {
    await this.contaBancoRepository.save(convenioBanco);
  }
}
