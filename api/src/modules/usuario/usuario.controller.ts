import { Controller, Get, Param } from "@nestjs/common";
import { UsuarioService } from "./usuario.service.js";

@Controller("usuario")
//@UseGuards(AuthGuard)
export class UsuarioController {
  constructor(private readonly usuarioService: UsuarioService) {}

  @Get()
  async findAll() {
    return this.usuarioService.getUsuarios();
  }

  @Get("/:id")
  async findUsuarioPorId(@Param("id") id: string) {
    return this.usuarioService.getUsuarioPorId(+id);
  }
}
