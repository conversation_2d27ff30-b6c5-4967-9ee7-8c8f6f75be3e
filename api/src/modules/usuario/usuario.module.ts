import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { SharedModule } from "../shared/shared.module.js";
import { UsuarioController } from "./usuario.controller.js";
import { Usuario } from "./usuario.entity.js";
import { UsuarioService } from "./usuario.service.js";

@Module({
  imports: [TypeOrmModule.forFeature([Usuario]), SharedModule],
  controllers: [UsuarioController],
  providers: [UsuarioService],
  exports: [UsuarioService],
})
export class UsuarioModule {}
