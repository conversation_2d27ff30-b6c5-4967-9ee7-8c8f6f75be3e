import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Get,
  Param,
  Patch,
  UseGuards,
  UseInterceptors,
} from "@nestjs/common";
import { endpoints, type RespostaRota } from "cpesc-shared/out/endpoints/main.js";
import { AuthGuard } from "../auth/auth.guard.js";
import { MovimentacaoService } from "./movimentacao.service.js";

@Controller("movimentacao")
@UseGuards(AuthGuard)
export class MovimentacaoController {
  constructor(private readonly movimentacaoService: MovimentacaoService) {}

  @UseInterceptors(ClassSerializerInterceptor)
  @Get()
  async findAll() {
    return this.movimentacaoService.getAllMovimentacao();
  }

  @Get("/com-notas/:id")
  async findById(@Param("id") id: string) {
    return this.movimentacaoService.getMovimentacaoPorId(+id);
  }

  @Get("/:id")
  async findOne(@Param("id") id: string) {
    return this.movimentacaoService.getMovimentacaoPorId(+id);
  }

  @Patch("/associar-limite")
  async associarLimite(
    @Body() body: { movimentacaoIds: number[]; limiteCartaoId: number },
  ): RespostaRota<typeof endpoints.associarLimiteMovimentacao> {
    const { movimentacaoIds, limiteCartaoId } = body;
    await this.movimentacaoService.associarLimite(movimentacaoIds, limiteCartaoId);

    return { sucesso: true };
  }

  @Patch("/:id")
  async removerVinculoNotaFiscal(@Param("id") id: string): RespostaRota<typeof endpoints.excluirVinculo> {
    const resultado = await this.movimentacaoService.removerVinculoNotaFiscal(+id);
    return { sucesso: resultado };
  }
}
