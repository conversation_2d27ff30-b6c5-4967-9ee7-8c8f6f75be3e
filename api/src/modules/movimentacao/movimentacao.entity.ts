import { Movimentacao } from "cpesc-shared";
import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  <PERSON>in<PERSON><PERSON><PERSON>n,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import type { ApiCartao } from "../cartao/cartao.entity.js";
import type { ApiLimiteCartao } from "../limite-cartao/limite-cartao.entity.js";
import type { ApiNotaFiscal } from "../nota-fiscal/nota-fiscal.entity.js";
import type { ApiPortador } from "../portador/portador.entity.js";
import type { ApiReversaoMovimentacao } from "../reversao-movimentacao/reversao-movimentacao.entity.js";
import type { ApiUnidadeAdministrativa } from "../unidade-administrativa/unidade-administrativa.entity.js";

@Entity("CPESC_MOVIMENTACAO")
export class ApiMovimentacao implements Movimentacao {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "NUMEROCARTAO" })
  numeroCartao: number;

  @Column({ name: "NUCONTACARTAO" })
  nuContaCartao: number;

  @Column({ name: "DATARECEBIMENTO" })
  dataRecebimento: Date;

  @Column({ name: "NOMEESTABELECIMENTO" })
  nomeEstabelecimento: string;

  @Column({ name: "CIDADEESTABELECIMENTO" })
  cidadeEstabelecimento: string;

  @Column({ name: "UFESTABELECIMENTO" })
  ufEstabelecimento: string;

  @Column({ name: "CEPESTABELECIMENTO" })
  cepEstabelecimento: string;

  @Column({ name: "VALORTRANSACAOREAL", type: "decimal", precision: 10, scale: 2 })
  valorTransacaoReal: number;

  @Column({ name: "DATATRANSACAO" })
  dataTransacao: Date;

  @Column({ name: "CODIGOTRANSACAOBB" })
  codigoTransacaoBB: number;

  @Column({ name: "DESCRICAOTRANSACAO" })
  descricaoTransacao: string;

  @Column({ name: "CNPJESTABELECIMENTO" })
  // eslint-disable-next-line @typescript-eslint/naming-convention -- TODO: padronizar nome
  CNPJEstabelecimento: string;

  @Column({ name: "HORATRANSACAO" })
  horaTransacao: string;

  @Column({ name: "TIPOMOVIMENTACAO" })
  tipoMovimentacao: number;

  @Column({ name: "LIMITECARTAO_ID", nullable: true })
  limiteCartaoId: number | null;

  @CreateDateColumn({ name: "CRIADOEM" })
  criadoEm: Date;

  @UpdateDateColumn({ name: "ATUALIZADOEM" })
  atualizadoEm: Date;

  @ManyToOne("ApiPortador", (p: ApiPortador) => p.cpf)
  @JoinColumn({ name: "CPFPORTADOR", referencedColumnName: "cpf" })
  portador: ApiPortador;

  @ManyToOne("ApiCartao", (c: ApiCartao) => c.nuContaCartao)
  @JoinColumn({ name: "NUCONTACARTAO", referencedColumnName: "nuContaCartao" })
  cartaoMovimentacao: ApiCartao;

  @ManyToOne("ApiUnidadeAdministrativa")
  @JoinColumn({ name: "UNIDADEADMINISTRATIVA_ID" })
  unidadeAdministrativa: ApiUnidadeAdministrativa;

  @OneToOne("ApiNotaFiscal", (notaFiscal: ApiNotaFiscal) => notaFiscal.movimentacao)
  notaFiscal: ApiNotaFiscal | null;

  @ManyToOne("ApiLimiteCartao")
  @JoinColumn({ name: "LIMITECARTAO_ID" })
  limiteCartao: ApiLimiteCartao;

  @OneToOne("ApiReversaoMovimentacao", (reversao: ApiReversaoMovimentacao) => reversao.reversao)
  @JoinColumn({ name: "ID", referencedColumnName: "movimentacaoId" })
  reversao: ApiReversaoMovimentacao;
}
