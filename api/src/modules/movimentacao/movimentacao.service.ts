import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { IsNull, LessThanOrEqual, Raw, Repository } from "typeorm";
import { ApiMovimentacao } from "./movimentacao.entity.js";
import { NotaFiscalService } from "../nota-fiscal/nota-fiscal.service.js";

@Injectable()
export class MovimentacaoService {
  constructor(
    @InjectRepository(ApiMovimentacao) private movimentacaoRepository: Repository<ApiMovimentacao>,
    private notaFiscalService: NotaFiscalService,
  ) {}

  async getAllMovimentacao(): Promise<ApiMovimentacao[]> {
    return this.movimentacaoRepository.find({
      where: {
        id: Raw(() => "ROWNUM <= 10"),
      },
      relations: {
        portador: true,
        notaFiscal: true,
      },
    });
  }

  async getMovimentacaoPorId(id: number): Promise<ApiMovimentacao | null> {
    const movimentacao = await this.movimentacaoRepository.findOne({
      where: { id },
      relations: {
        reversao: true,
        portador: true,
        notaFiscal: { notafiscalitens: { itemfiscal: true } },
      },
    });

    if (!movimentacao) {
      return null;
    }

    movimentacao.valorTransacaoReal =
      movimentacao.codigoTransacaoBB === 253600
        ? movimentacao.valorTransacaoReal * -1
        : movimentacao.valorTransacaoReal;
    return movimentacao;
  }

  async getMovimentacoesPorPeriodo(
    limiteId: number,
    fimPeriodo: Date,
    nuContaCartao: number,
  ): Promise<ApiMovimentacao[]> {
    
    const movimentacoes = await this.movimentacaoRepository.find({
      where: [
        {
          nuContaCartao,
          dataTransacao: LessThanOrEqual(fimPeriodo),
          limiteCartaoId: IsNull(),
        },
        {
          nuContaCartao,
          dataTransacao: LessThanOrEqual(fimPeriodo),
          limiteCartaoId: limiteId,
        },
      ],
      relations: {
        reversao: true,
        notaFiscal: { notafiscalitens: true },
      },
      order: { dataTransacao: "ASC", valorTransacaoReal: "DESC" },
    });

    return movimentacoes.map(gasto => ({
      ...gasto,
      nomeEstabelecimento: gasto.nomeEstabelecimento ? gasto.nomeEstabelecimento.length > 3 ? gasto.nomeEstabelecimento : gasto.descricaoTransacao : gasto.descricaoTransacao,
      CNPJEstabelecimento: gasto.CNPJEstabelecimento === "00000000000000" ? "" : gasto.CNPJEstabelecimento,
      valorTransacaoReal: gasto.codigoTransacaoBB === 253600 ? gasto.valorTransacaoReal * -1 : gasto.valorTransacaoReal,
    }));
  }

  async removerVinculoNotaFiscal(id: number): Promise<boolean> {
    try {
      const movimentacao = await this.movimentacaoRepository.findOne({
        where: { id },
        relations: { notaFiscal: { notafiscalitens: true } },
      });

      if (!movimentacao) {
        return false;
      }

      const notaId = movimentacao.notaFiscal?.id;

      if (notaId) {
        await this.notaFiscalService.removeNotaFiscalENotaFiscalItem(notaId);
      }

      movimentacao.limiteCartaoId = null;
      movimentacao.notaFiscal = null;

      await this.movimentacaoRepository.save(movimentacao);

      return true;
    } catch (error) {
      console.error("Erro ao remover vínculo com a nota fiscal:", error);
      return false;
    }
  }

  async removerMovimentacoesVinculados(id: number): Promise<boolean> {
    try {
      const movimentacao = await this.movimentacaoRepository.findOne({
        where: { limiteCartaoId: id },
      });

      if (movimentacao) {
        movimentacao.limiteCartaoId = null;
        await this.movimentacaoRepository.save(movimentacao);
      }
      return true;
    } catch (error) {
      console.error("Erro ao remover movimentações vinculadas:", error);
      return false;
    }
  }

  async associarLimite(movimentacaoIds: number[], limiteCartaoId: number): Promise<void> {
    for (const id of movimentacaoIds) {
      const movimentacao = await this.movimentacaoRepository.findOne({ where: { id } });
      if (movimentacao) {
        movimentacao.limiteCartaoId = limiteCartaoId;
        await this.movimentacaoRepository.save(movimentacao);
      }
    }
  }
}
