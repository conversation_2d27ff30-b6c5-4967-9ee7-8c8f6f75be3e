import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { ApiMovimentacao } from "./movimentacao.entity.js";
import { MovimentacaoService } from "./movimentacao.service.js";

describe("MovimentacaoService", () => {
  let service: MovimentacaoService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [MovimentacaoService, { provide: getRepositoryToken(ApiMovimentacao), useFactory: jest.fn }],
    }).compile();

    service = module.get<MovimentacaoService>(MovimentacaoService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
