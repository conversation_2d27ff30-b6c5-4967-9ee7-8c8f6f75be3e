import { forwardRef, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AuthModule } from "../auth/auth.module.js";
import { PortadorModule } from "../portador/portador.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { UsuarioModule } from "../usuario/usuario.module.js";
import { MovimentacaoController } from "./movimentacao.controller.js";
import { ApiMovimentacao } from "./movimentacao.entity.js";
import { MovimentacaoService } from "./movimentacao.service.js";
import { LimiteCartaoModule } from "../limite-cartao/limite-cartao.module.js";
import { NotaFiscalModule } from "../nota-fiscal/nota-fiscal.module.js";
@Module({
  imports: [
    TypeOrmModule.forFeature([ApiMovimentacao]),
    SharedModule,
    UsuarioModule,
    forwardRef(() => LimiteCartaoModule),
    forwardRef(() => PortadorModule),
    forwardRef(() => AuthModule),
    NotaFiscalModule,
  ],
  controllers: [MovimentacaoController],
  providers: [MovimentacaoService],
  exports: [MovimentacaoService],
})
export class MovimentacaoModule {}
