import { Controller, Delete, Get, Param, UseGuards } from "@nestjs/common";
import { endpoints, type RespostaRota } from "cpesc-shared/out/endpoints/main.js";
import { AuthGuard } from "../auth/auth.guard.js";
import { ItemFiscalService } from "./item-fiscal.service.js";

@Controller("item-fiscal")
@UseGuards(AuthGuard)
export class ItemFiscalController {
  constructor(private readonly itemFiscalService: ItemFiscalService) {}

  @Get()
  async findAll(): RespostaRota<typeof endpoints.buscarItensFiscais> {
    return this.itemFiscalService.findAll();
  }

  @Get("/:busca/:tipo")
  async find(
    @Param("busca") busca: string,
    @Param("tipo") tipoGasto: string,
  ): RespostaRota<typeof endpoints.buscarItensFiscaisPorNcmETipoGasto> {
    return this.itemFiscalService.getNcmBySearch(busca.toUpperCase(), tipoGasto.toUpperCase());
  }

  @Delete("/:id")
  async remove(@Param("id") id: string) {
    return this.itemFiscalService.remove(+id);
  }
}
