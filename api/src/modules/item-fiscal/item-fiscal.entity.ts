import { ItemFiscal } from "cpesc-shared";
import { Column, <PERSON>tity, PrimaryGeneratedColumn } from "typeorm";

@Entity("CPESC_ITEMFISCAL")
export class ApiItemFiscal implements ItemFiscal {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "NCM" })
  ncm: string;

  @Column({ name: "DESCRICA<PERSON>" })
  descricao: string;

  @Column({ name: "TIPO" })
  tipo: string;

  @Column({ name: "PESQUISA" })
  pesquisa: string;
}
