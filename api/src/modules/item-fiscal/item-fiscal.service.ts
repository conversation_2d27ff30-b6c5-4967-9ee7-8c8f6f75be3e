import { BadRequestException, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Like, Repository } from "typeorm";
import { ApiItemFiscal } from "./item-fiscal.entity.js";

@Injectable()
export class ItemFiscalService {
  constructor(@InjectRepository(ApiItemFiscal) private notaFiscalItemRepository: Repository<ApiItemFiscal>) {}

  async findAll() {
    return this.notaFiscalItemRepository.find();
  }

  async getNcmBySearch(params: string, tipoGasto: string): Promise<ApiItemFiscal[]> {
    if (params.length < 4) {
      throw new BadRequestException();
    }
    const numericRegex = /\d+/g;
    const matchResult = params.match(numericRegex);
    const paramsNum = matchResult?.[0] ?? "";

    const paramsUpper = params.toUpperCase();
    const resposta: ApiItemFiscal[] = [];

    //Busca as raizes das descricoes - 4 e/ou 2 caracteres
    if (Number.isInteger(parseInt(paramsNum, 10))) {
      const ncmByNumber = await this.getNCMByNumber(paramsNum, tipoGasto);
      if (ncmByNumber) {
        resposta.push(ncmByNumber);
      }
    }

    if (resposta.length < 1) {
      const ncmsEncontrados = await this.notaFiscalItemRepository.find({
        where: [
          { pesquisa: Like(`% ${paramsUpper}%`), tipo: tipoGasto },
          { pesquisa: Like(`%${paramsUpper} %`), tipo: tipoGasto },
        ],
        order: {
          ncm: "ASC",
        },
      });
      for (const item of ncmsEncontrados) {
        const ncmByNumber = await this.getNCMByNumber(item.ncm, tipoGasto);
        if (ncmByNumber) {
          resposta.push(ncmByNumber);
        }
      }
    }
    return resposta;
  }

  async getNCMByNumber(ncm: string, tipoGasto: string): Promise<ApiItemFiscal | null> {
    const ncmNumber8 = ncm.slice(0, 8);
    const ncmNumber6 = ncm.slice(0, 6);
    const ncmNumber4 = ncm.slice(0, 4);
    const ncmNumber2 = ncm.slice(0, 2);
    let descricaoNCM = "";

    if (!isNaN(parseInt(ncm, 10))) {
      const respostaNCM8 = await this.notaFiscalItemRepository.findOne({ where: { ncm: ncmNumber8, tipo: tipoGasto } });
      if (respostaNCM8) {
        descricaoNCM = respostaNCM8.descricao;
        if (ncmNumber6 != ncmNumber8) {
          const respostaNCM6 = await this.notaFiscalItemRepository.findOne({
            where: { ncm: ncmNumber6, tipo: tipoGasto },
          });
          if (respostaNCM6) {
            descricaoNCM = `${respostaNCM6.descricao} ${descricaoNCM}`;
          }
        }
        if (ncmNumber4 != ncmNumber8) {
          const respostaNCM4 = await this.notaFiscalItemRepository.findOne({
            where: { ncm: ncmNumber4, tipo: tipoGasto },
          });
          if (respostaNCM4) {
            descricaoNCM = `${respostaNCM4.descricao} ${descricaoNCM}`;
          }
        }
        const hasOutr = descricaoNCM.toUpperCase().slice(0, 7).includes("OUTR"); //Consta a palavra OUTR no inicio
        if (hasOutr) {
          const respostaNCM2 = await this.notaFiscalItemRepository.findOne({
            where: { ncm: ncmNumber2, tipo: tipoGasto },
          });
          if (respostaNCM2) {
            descricaoNCM = `${respostaNCM2.descricao} ${descricaoNCM}`;
          }
        }
        respostaNCM8.descricao = descricaoNCM;
        return respostaNCM8;
      }
    }
    return null;
  }

  async remove(idPar: number) {
    return this.notaFiscalItemRepository.delete({ id: idPar });
  }
}
