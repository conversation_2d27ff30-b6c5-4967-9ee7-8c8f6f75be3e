import { forwardRef, Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AuthModule } from "../auth/auth.module.js";
import { PortadorModule } from "../portador/portador.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { UsuarioModule } from "../usuario/usuario.module.js";
import { ApiItemFiscal } from "./item-fiscal.entity.js";
import { ItemFiscalService } from "./item-fiscal.service.js";
import { ItemFiscalController } from "./item-fiscal.controller.js";

@Module({
  controllers: [ItemFiscalController],
  providers: [ItemFiscalService],
  imports: [
    TypeOrmModule.forFeature([ApiItemFiscal]),
    forwardRef(() => AuthModule),
    forwardRef(() => PortadorModule),
    UsuarioModule,
    SharedModule,
  ],
  exports: [ItemFiscalService],
})
export class ItemFiscalModule {}
