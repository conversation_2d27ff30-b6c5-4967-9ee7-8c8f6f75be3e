import { Controller, Delete, Get, Param, UseGuards } from "@nestjs/common";
import { AuthGuard } from "../auth/auth.guard.js";
import { NotaFiscalItemService } from "./nota-fiscal-item.service.js";

@Controller("nota-fiscal-item")
@UseGuards(AuthGuard)
export class NotaFiscalItemController {
  constructor(private readonly notaFiscalItemService: NotaFiscalItemService) {}

  @Get()
  async findAll() {
    return this.notaFiscalItemService.findAll();
  }

  @Get(":id")
  async findOne(@Param("id") id: string) {
    return this.notaFiscalItemService.findOneByDynamic({ id: parseInt(id) });
  }

  @Delete(":id")
  async remove(@Param("id") id: string) {
    return this.notaFiscalItemService.remove(parseInt(id));
  }
}
