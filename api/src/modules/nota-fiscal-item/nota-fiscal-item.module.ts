import { forwardRef, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AuthModule } from "../auth/auth.module.js";
import { PortadorModule } from "../portador/portador.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { UsuarioModule } from "../usuario/usuario.module.js";
import { NotaFiscalItemController } from "./nota-fiscal-item.controller.js";
import { ApiNotaFiscalItem } from "./nota-fiscal-item.entity.js";
import { NotaFiscalItemService } from "./nota-fiscal-item.service.js";
import { ItemFiscalModule } from "../item-fiscal/item-fiscal.module.js";

@Module({
  controllers: [NotaFiscalItemController],
  providers: [NotaFiscalItemService],
  imports: [
    TypeOrmModule.forFeature([ApiNotaFiscalItem]),
    forwardRef(() => PortadorModule),
    SharedModule,
    forwardRef(() => AuthModule),
    UsuarioModule,
    ItemFiscalModule,
  ],
  exports: [NotaFiscalItemService],
})
export class NotaFiscalItemModule {}
