import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { ApiItemFiscal } from "../item-fiscal/item-fiscal.entity.js";
import { ItemFiscalService } from "../item-fiscal/item-fiscal.service.js";
import { ApiNotaFiscalItem } from "./nota-fiscal-item.entity.js";
import { NotaFiscalItemService } from "./nota-fiscal-item.service.js";

describe("NotaFiscalItemService", () => {
  let service: NotaFiscalItemService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ItemFiscalService,
        NotaFiscalItemService,
        { provide: getRepositoryToken(ApiItemFiscal), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiNotaFiscalItem), useFactory: jest.fn },
      ],
    }).compile();

    service = module.get<NotaFiscalItemService>(NotaFiscalItemService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
