import { NotaFiscalItem } from "cpesc-shared";
import {
  <PERSON>um<PERSON>,
  CreateDateColumn,
  Entity,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { ApiItemFiscal } from "../item-fiscal/item-fiscal.entity.js";
import type { ApiNotaFiscal } from "../nota-fiscal/nota-fiscal.entity.js";

@Entity("CPESC_NOTAFISCALITEM")
export class ApiNotaFiscalItem implements NotaFiscalItem {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "NOTAFISCAL_ID" })
  // eslint-disable-next-line @typescript-eslint/naming-convention -- TODO: padronizar nome
  notafiscal_id: number;

  @Column({ name: "ITEMFISCAL_ID" })
  // eslint-disable-next-line @typescript-eslint/naming-convention -- TODO: padronizar nome
  itemfiscal_id: number | null;

  @Column({ name: "NCM", nullable: true, type: String })
  ncm: string;

  @Column({ name: "DESCRICAO", nullable: true, type: String })
  descricao: string;

  @Column({ name: "QUANTIDADE" })
  quantidade: number;

  @Column({ name: "UNIDADE" })
  unidade: string;

  @Column({ name: "VALORUNITARIO", type: "decimal" })
  valorunitario: number | null;

  @Column({ name: "VALOR", type: "decimal" })
  valor: number;

  @Column({ name: "CRIADOPOR" })
  criadopor: string;

  @CreateDateColumn({ name: "CRIADOEM" })
  criadoem: Date;

  @Column({ name: "ATUALIZADOPOR" })
  atualizadopor: string;

  @UpdateDateColumn({ name: "ATUALIZADOEM" })
  atualizadoem: Date;

  @ManyToOne("ApiNotaFiscal", (nf: ApiNotaFiscal) => nf.id)
  @JoinColumn({ name: "NOTAFISCAL_ID" })
  notafiscal: ApiNotaFiscal;

  @OneToOne("ApiItemFiscal", (itemFiscal: ApiItemFiscal) => itemFiscal.id)
  @JoinColumn({ name: "ITEMFISCAL_ID" })
  itemfiscal: ApiItemFiscal | null;
}
