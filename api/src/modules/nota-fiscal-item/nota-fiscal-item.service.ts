import { Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { ItemFiscal } from "cpesc-shared";
import { PayloadCriarNFItem } from "cpesc-shared/out/endpoints/nota-fiscal-item.js";
import { FindOptionsWhere, Repository } from "typeorm";
import { ItemFiscalService } from "../item-fiscal/item-fiscal.service.js";
import { ApiNotaFiscalItem } from "./nota-fiscal-item.entity.js";
import { DataSource } from "typeorm";

@Injectable()
export class NotaFiscalItemService {
  constructor(
    @InjectRepository(ApiNotaFiscalItem) private notaFiscalItemRepository: Repository<ApiNotaFiscalItem>,
    private itemFiscalService: ItemFiscalService,
    private dataSource: DataSource,
  ) {}

  async criarItemManual(
    dadosNotaFiscalItens: PayloadCriarNFItem[],
    notafiscalid: number,
    email: string,
  ): Promise<void> {
    try {
      const itens = dadosNotaFiscalItens.map(
        (item): Omit<ApiNotaFiscalItem, "id" | "notafiscal"> => ({
          ...item,
          ncm: item.ncm,
          valorunitario: item.valorunitario,
          descricao: item.descricao,
          notafiscal_id: notafiscalid,
          criadopor: email,
          criadoem: new Date(),
          atualizadopor: email,
          atualizadoem: new Date(),
        }),
      );

      const nf = this.notaFiscalItemRepository.create(itens);
      await this.notaFiscalItemRepository.save(nf);
    } catch (error) {
      console.log("Erro ao salvar item de nota fiscal", error);
    }
  }

  async atualizarItem(
    dadosNotaFiscalItem: Omit<ApiNotaFiscalItem, "itemfiscal">[],
    notaFiscalId: number,
    email: string,
  ): Promise<void> {
    try {
      // 1. Buscar todos os IDs de itens existentes para esta nota fiscal
      const itensExistentes = await this.notaFiscalItemRepository.find({
        where: { notafiscal_id: notaFiscalId },
        select: { id: true },
      });

      // 2. Separar itens para atualizar e itens novos
      const itensParaAtualizar: ApiNotaFiscalItem[] | null = [];
      const itensNovos: Omit<ApiNotaFiscalItem, "id" | "itemfiscal">[] = [];

      // Mapear IDs dos itens recebidos que já existem
      const idsRecebidos = new Set<number>();

      dadosNotaFiscalItem.forEach(item => {
        if ("id" in item && item.id > 0) {
          idsRecebidos.add(item.id);
          itensParaAtualizar.push({
            ...item,
            atualizadopor: email,
            atualizadoem: new Date(),
            itemfiscal: null,
          });
        } else {
          itensNovos.push({
            ...item,
            notafiscal_id: notaFiscalId,
            criadopor: email,
            criadoem: new Date(),
            atualizadopor: email,
            atualizadoem: new Date(),
          });
        }
      });

      // 3. Identificar IDs para remover (existentes mas não recebidos)
      const idsParaRemover = itensExistentes.filter(item => !idsRecebidos.has(item.id)).map(item => item.id);

      // 4. Executar as operações sequencialmente
      // Remover itens que não existem mais
      if (idsParaRemover.length > 0) {
        await this.notaFiscalItemRepository.delete(idsParaRemover);
      }

      // Atualizar itens existentes
      if (itensParaAtualizar.length > 0) {
        await this.notaFiscalItemRepository.save(itensParaAtualizar);
      }

      // Inserir novos itens
      if (itensNovos.length > 0) {
        const novosItens = this.notaFiscalItemRepository.create(itensNovos);
        await this.notaFiscalItemRepository.save(novosItens);
      }
    } catch (error) {
      console.log("Erro ao atualizar itens de nota fiscal", error);
    }
  }

  async findAll() {
    return this.notaFiscalItemRepository.find({
      relations: { itemfiscal: true },
    });
  }

  async findOneByDynamic(params: FindOptionsWhere<ApiNotaFiscalItem>) {
    return this.notaFiscalItemRepository.findOneBy(params);
  }

  async remove(idPar: number) {
    return this.notaFiscalItemRepository.delete({ id: idPar });
  }

  async removeByNotaFiscalId(notaFiscalId: number): Promise<void> {
    await this.notaFiscalItemRepository.delete({ notafiscal_id: notaFiscalId });
  }

  async getItemFiscalId(ncmProduto: string, tipoGasto: string): Promise<number | null> {
    const itemFiscal: ItemFiscal[] = await this.itemFiscalService.getNcmBySearch(ncmProduto, tipoGasto);
    return itemFiscal[0].id ?? null;
  }

  async getNotaFiscalItemNoSIGEF(
    codigosigef: number,
    idnota: number,
    email: string,
  ): Promise<Partial<ApiNotaFiscalItem>[]> {
    try {
      const dados: {
        NCMPRODUTO: string | null;
        DESCRICAO: string;
        QUANTIDADE: number;
        VALOR: number;
        VALORUNITARIO: number;
        UNIDADE: string;
      }[] = await this.dataSource.query(
        `SELECT CDNCMPRODUTO AS NCMPRODUTO, DEPRODUTO AS DESCRICAO, QTCOMERCIAL AS QUANTIDADE, DEUNIDCOMERCIAL AS UNIDADE, VLTOTAL AS VALOR, VLUNITCOMERCIAL AS VALORUNITARIO
           FROM SIGEF.EFINNOTAFISCALSATITEM@SIGEFCPESC
           WHERE NUNFEID = :pCodSigef`,
        [codigosigef],
      );

      const itens: Partial<ApiNotaFiscalItem>[] = dados.map(element => {
        return {
          //itemfiscal_id: await this.getItemFiscalId(element.NCMPRODUTO ?? "0", "M"), //TODO caso venha a exisitir nota de servicos automaticas refazer esta parte do codigo
          ncm: element.NCMPRODUTO,
          descricao: element.DESCRICAO,
          quantidade: element.QUANTIDADE,
          valor: element.VALOR,
          valorunitario: element.VALORUNITARIO,
          unidade: element.UNIDADE,
          criadoem: new Date(),
          atualizadoem: new Date(),
          notafiscal_id: idnota,
          criadopor: email,
          atualizadopor: email,
        };
      }) as unknown as Partial<ApiNotaFiscalItem>[];
      return await Promise.all(itens);
    } catch (error) {
      console.error("Erro ao buscar itens da Nota Fiscal no SIGEF:", error);
      throw new InternalServerErrorException("Falha ao buscar itens da Nota Fiscal no SIGEF");
    }
  }

  async criarItemBuscandoSigef(codigosigef: number, idNotaFiscal: number, email: string) {
    try {
      const itens = await this.getNotaFiscalItemNoSIGEF(codigosigef, idNotaFiscal, email);
      if (itens.length > 0) {
        const nfi = this.notaFiscalItemRepository.create(itens);
        await this.notaFiscalItemRepository.save(nfi);
      }
      return;
    } catch (error) {
      console.log("Erro ao criar item de nota fiscal", error);
      return;
    }
  }
}
