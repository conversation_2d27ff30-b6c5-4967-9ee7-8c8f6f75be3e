import { BadRequestException, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Credito, CreditoCartao, Perfil, SituacaoPrestacaoConta, UsuarioAutenticado } from "cpesc-shared";
import { assertNotNull } from "cpesc-shared/out/assertions.js";
import { IsNull, Raw, Repository } from "typeorm";
import { MovimentacaoService } from "../movimentacao/movimentacao.service.js";
import { ApiLimiteCartao } from "./limite-cartao.entity.js";

@Injectable()
export class LimiteCartaoService {
  constructor(
    @InjectRepository(ApiLimiteCartao) private limiteCartaoRepository: Repository<ApiLimiteCartao>,
    private movimentacaoService: MovimentacaoService,
  ) {}

  async getLimiteCartoes(): Promise<ApiLimiteCartao[]> {
    return this.limiteCartaoRepository.find({
      where: {
        id: Raw(() => "ROWNUM <= 10"),
      },
      order: { dataCredito: "DESC", prestacao: { id: "DESC" } },
    });
  }

  async getGastosPorCreditoId(usuario: UsuarioAutenticado, idCredito: number): Promise<Credito> {
    const limitePrestacoes: Credito | null = await this.limiteCartaoRepository.findOne({
      where: {
        id: idCredito,
        ...(usuario.perfil === Perfil.Portador && {
          cartao: {
            portadorUnidadeAdministrativa: {
              portador: { id: usuario.id },
            },
          },
        }),
      },
      relations: {
        prestacao: true,
        cartao: {
          portadorUnidadeAdministrativa: {
            portador: true,
          },
        },
      },
      order: { dataCredito: "ASC" },
    });

    if (limitePrestacoes == null) {
      throw new BadRequestException("Limite de crédito não encontrado para este portador.");
    }

    let situacaoPrestacao = SituacaoPrestacaoConta.Pendente;
    for (const prestacao of limitePrestacoes.prestacao) {
      if (prestacao.situacao === SituacaoPrestacaoConta.Realizada) {
        situacaoPrestacao = SituacaoPrestacaoConta.Realizada;
      }
    }

    if (situacaoPrestacao === SituacaoPrestacaoConta.Realizada) {
      const limiteComMovimentacoes = await this.getGastosPorCreditoIdAssociados(idCredito);
      assertNotNull(limiteComMovimentacoes, "Limite com movimentações não encontrado.");
      limiteComMovimentacoes.temMaisLimites = false;
      return limiteComMovimentacoes;
    } else {
      const temMaisLimites = await this.temMaisLimites(
        limitePrestacoes.cartao.portadorUnidadeAdministrativa.portador.id,
        limitePrestacoes.dataInicioMovimentacao,
      );

      const limiteSemMovimentacoes = await this.limiteCartaoRepository.findOne({
        where: { id: idCredito },
        relations: {
          cartao: {
            portadorUnidadeAdministrativa: {
              portador: true,
              unidadeAdministrativa: { municipio: true, unidadeGestora: { contaBanco: true } },
            },
          },
          subelemento: true,
          prestacao: true,
        },
      });

      if (limiteSemMovimentacoes !== null) {
        const movimentacoes = await this.movimentacaoService.getMovimentacoesPorPeriodo(
          idCredito,
          limiteSemMovimentacoes.dataLimiteMovimentacao,
          limiteSemMovimentacoes.cartao.nuContaCartao,
        );
        (limiteSemMovimentacoes as CreditoCartao).temMaisLimites = temMaisLimites;
        limiteSemMovimentacoes.cartao.movimentacoes = movimentacoes;
        return limiteSemMovimentacoes;
      } else {
        return limitePrestacoes;
      }
    }
  }

  async getGastosPorCreditoIdAssociados(idCredito: number): Promise<Credito | null> {
    const gastos = await this.limiteCartaoRepository.findOne({
      where: {
        id: idCredito,
      },
      relations: {
        movimentacoes: {
          reversao: true,
          notaFiscal: { notafiscalitens: { itemfiscal: true } },
        },
        cartao: {
          portadorUnidadeAdministrativa: {
            portador: true,
            unidadeAdministrativa: { municipio: true, unidadeGestora: { contaBanco: true } },
          },
        },
        subelemento: true,
        prestacao: true,
      },
      order: {
        movimentacoes: {
          dataTransacao: "ASC",
          valorTransacaoReal: "DESC",
        },
      },
    });

    if (gastos?.movimentacoes) {
      gastos.movimentacoes = gastos.movimentacoes.map(mov => ({
        ...mov,
        nomeEstabelecimento: mov.nomeEstabelecimento.length > 1 ? mov.nomeEstabelecimento : mov.descricaoTransacao,
        CNPJEstabelecimento: mov.CNPJEstabelecimento === "00000000000000" ? "" : mov.CNPJEstabelecimento,
        valorTransacaoReal: mov.codigoTransacaoBB === 253600 ? mov.valorTransacaoReal * -1 : mov.valorTransacaoReal,
      }));
    }

    return gastos;
  }

  async temMaisLimites(idPortador: number, dataInicial: Date): Promise<boolean> {
    if (!idPortador) return false;

    const limites = await this.limiteCartaoRepository.find({
      where: {
        dataInicioMovimentacao: dataInicial,
        cartao: {
          portadorUnidadeAdministrativa: {
            portador: { id: idPortador },
          },
        },
        prestacao: { limiteCartaoId: IsNull() },
      },
    });

    if (limites.length > 1) return true;

    return false;
  }

  async getLimitesPorPortadorId(id: number): Promise<ApiLimiteCartao[]> {
    return this.limiteCartaoRepository.find({
      where: {
        cartao: { portadorUnidadeAdministrativa: { portador: { id } } },
      },
      relations: {
        subelemento: true,
        prestacao: true,
        cartao: {
          portadorUnidadeAdministrativa: {
            unidadeAdministrativa: { municipio: true, unidadeGestora: true },
            portador: true,
          },
        },
      },
      order: { dataCredito: "DESC", prestacao: { id: "DESC" } },
    });
  }

  async getLimitesPorPortadorUa(idPortador: number, idUa: number): Promise<ApiLimiteCartao[]> {
    const limites = await this.limiteCartaoRepository.find({
      where: {
        cartao: {
          portadorUnidadeAdministrativa: {
            portador: { id: idPortador },
            unidadeAdministrativa: { id: idUa },
          },
        },
      },
      relations: {
        subelemento: true,
        prestacao: true,
        cartao: { portadorUnidadeAdministrativa: { unidadeAdministrativa: { municipio: true, unidadeGestora: true } } },
      },
      order: { dataCredito: "DESC", prestacao: { id: "DESC", situacao: "DESC" } },
    });
    for (const limite of limites) {
      if (limite.prestacao.some(prestacao => prestacao.situacao === SituacaoPrestacaoConta.Realizada)) {
        limite.prestacao = limite.prestacao.filter(
          prestacao => prestacao.situacao === SituacaoPrestacaoConta.Realizada,
        );
      }
    }
    return limites;
  }

  async updateGastosCartao(idCredito: number, updateLimiteCartao: Partial<ApiLimiteCartao>): Promise<ApiLimiteCartao> {
    const limiteExistente = await this.limiteCartaoRepository.findOne({
      where: { id: idCredito },
      relations: { prestacao: true, cartao: { movimentacoes: true } },
    });
    assertNotNull(limiteExistente, "Limite de cartão não encontrado.");
    const limite = this.limiteCartaoRepository.merge(limiteExistente, updateLimiteCartao);

    return this.limiteCartaoRepository.save(limite);
  }

  async finalizarLimiteCartao(id: number): Promise<void> {
    const limite = await this.limiteCartaoRepository.findOne({ where: { id } });

    if (!limite) {
      throw new Error("Limite de cartão não encontrado.");
    }
    limite.dataLimiteMovimentacao = new Date(); // opcional, se quiser registrar data
    await this.limiteCartaoRepository.save(limite);
  }
}
