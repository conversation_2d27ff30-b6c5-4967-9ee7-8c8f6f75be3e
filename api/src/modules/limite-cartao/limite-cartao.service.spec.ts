import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { ApiMovimentacao } from "../movimentacao/movimentacao.entity.js";
import { MovimentacaoService } from "../movimentacao/movimentacao.service.js";
import { ApiUnidadeGestora } from "../unidade-gestora/unidade-gestora.entity.js";
import { UnidadeGestoraService } from "../unidade-gestora/unidade-gestora.service.js";
import { ApiLimiteCartao } from "./limite-cartao.entity.js";
import { LimiteCartaoService } from "./limite-cartao.service.js";

describe("LimiteCartaoService", () => {
  let service: LimiteCartaoService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LimiteCartaoService,
        MovimentacaoService,
        UnidadeGestoraService,
        { provide: getRepositoryToken(ApiLimiteCartao), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiMovimentacao), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiUnidadeGestora), useFactory: jest.fn },
      ],
    }).compile();

    service = module.get<LimiteCartaoService>(LimiteCartaoService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
