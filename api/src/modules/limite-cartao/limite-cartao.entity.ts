import { <PERSON><PERSON> } from "cpesc-shared";
import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, PrimaryGeneratedColumn } from "typeorm";
import type { ApiCartao } from "../cartao/cartao.entity.js";
import type { ApiMovimentacao } from "../movimentacao/movimentacao.entity.js";
import type { ApiPrestacaoContas } from "../prestacao-contas/prestacao-contas.entity.js";
import type { ApiSubelemento } from "../subelemento/subelemento.entity.js";
import type { ApiUnidadeGestora } from "../unidade-gestora/unidade-gestora.entity.js";

@Entity("CPESC_LIMITECARTAO")
export class ApiLimiteCartao implements Credito {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "DATACREDITO" })
  dataCredito: Date;

  @Column({ name: "DATAVENCIMENTO" })
  dataLimite: Date;

  @Column({ name: "DATAINICIOMOVIMENTACAO" })
  dataInicioMovimentacao: Date;

  @Column({ name: "DATALIMITEMOVIMENTACAO" })
  dataLimiteMovimentacao: Date;

  @Column({ name: "VALOR" })
  valorCredito: number;

  @Column({ name: "NUNOTAEMPENHO" })
  notaEmpenho: string;

  @Column({ name: "NUNOTALANCAMENTO" })
  notaLancamento: string;

  @Column({ name: "NUPREPARACAOPAGAMENTO" })
  preparacaopagamento: string;

  @Column({ name: "NUORDEMBANCARIA" })
  ordemBancaria: string;

  @Column({ name: "CDFONTE" })
  fonteRecurso: string;

  @Column({ name: "CDUNIDADEGESTORA" })
  cdUnidadeGestora: string;

  @ManyToOne("ApiSubelemento")
  @JoinColumn({ name: "SUBELEMENTO_ID" })
  subelemento: ApiSubelemento;

  @OneToMany("ApiPrestacaoContas", (pc: ApiPrestacaoContas) => pc.limite)
  prestacao: ApiPrestacaoContas[];

  @ManyToOne("ApiCartao")
  @JoinColumn({ name: "CARTAO_ID" })
  cartao: ApiCartao;

  @OneToMany("ApiMovimentacao", (m: ApiMovimentacao) => m.limiteCartao)
  movimentacoes: ApiMovimentacao[];

  @ManyToOne("ApiUnidadeGestora", (ug: ApiUnidadeGestora) => ug.limites)
  @JoinColumn([{ name: "CDUNIDADEGESTORA", referencedColumnName: "codigo" }])
  unidadeGestora: ApiUnidadeGestora;
}
