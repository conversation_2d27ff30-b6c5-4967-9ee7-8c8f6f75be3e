import { forwardRef, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AuthModule } from "../auth/auth.module.js";
import { UsuarioModule } from "../usuario/usuario.module.js";
import { PortadorModule } from "../portador/portador.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { MovimentacaoModule } from "../movimentacao/movimentacao.module.js";
import { LimiteCartaoController } from "./limite-cartao.controller.js";
import { ApiLimiteCartao } from "./limite-cartao.entity.js";
import { LimiteCartaoService } from "./limite-cartao.service.js";

@Module({
  imports: [
    TypeOrmModule.forFeature([ApiLimiteCartao]),
    SharedModule,
    UsuarioModule,
    forwardRef(() => PortadorModule),
    forwardRef(() => AuthModule),
    forwardRef(() => MovimentacaoModule),
  ],
  controllers: [LimiteCartaoController],
  providers: [LimiteCartaoService],
  exports: [LimiteCartaoService],
})
export class LimiteCartaoModule {}
