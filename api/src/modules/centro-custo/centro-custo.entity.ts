import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToMany, ManyToOne, PrimaryGeneratedColumn } from "typeorm";
import type { ApiContaBanco } from "../conta-banco/conta-banco.entity.js";
import type { ApiPortador } from "../portador/portador.entity.js";
import type { ApiUnidadeGestora } from "../unidade-gestora/unidade-gestora.entity.js";

@Entity("CPESC_CENTROCUSTO")
export class ApiCentroCusto {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "NOME" })
  nome: string;

  @Column({ name: "NUMEROBANCOCENTROCUSTO" })
  centroCusto: number;

  @Column({ name: "CALCULOPAGAMENTO_ID" })
  calculoPagamento: number;

  @Column({ name: "ATIVO" })
  ativo: string;

  @Column({ name: "ATUALIZADOPOR" })
  updatedBy: string;

  @Column({ name: "CRIADOPOR" })
  createdBy: string;

  @Column({ name: "ATUALIZADOEM" })
  updatedAt: Date;

  @Column({ name: "CRIADOEM" })
  createdAt: Date;

  @ManyToOne("ApiUnidadeGestora", (ug: ApiUnidadeGestora) => ug.id)
  @JoinColumn({ name: "UNIDADEGESTORA_ID" })
  unidadeGestora: ApiUnidadeGestora;

  @ManyToOne("ApiContaBanco", (cb: ApiContaBanco) => cb.id)
  @JoinColumn({ name: "CONVENIOBANCO_ID" })
  convenioBanco: ApiContaBanco;

  @ManyToMany("ApiPortador")
  @JoinColumn({ name: "PORTADORCARTAO_ID" })
  portadores: ApiPortador[];
}
