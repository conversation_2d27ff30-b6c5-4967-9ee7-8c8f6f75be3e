import { jest } from "@jest/globals";
import { ConfigService } from "@nestjs/config";
import { JwtService } from "@nestjs/jwt";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { TokenService } from "../../helpers/token.service.js";
import { ApiLimiteCartao } from "../limite-cartao/limite-cartao.entity.js";
import { LimiteCartaoService } from "../limite-cartao/limite-cartao.service.js";
import { ApiMovimentacao } from "../movimentacao/movimentacao.entity.js";
import { MovimentacaoService } from "../movimentacao/movimentacao.service.js";
import { ApiPortador } from "../portador/portador.entity.js";
import { PortadorService } from "../portador/portador.service.js";
import { EnvService } from "../shared/env.service.js";
import { ApiUnidadeGestora } from "../unidade-gestora/unidade-gestora.entity.js";
import { UnidadeGestoraService } from "../unidade-gestora/unidade-gestora.service.js";
import { Usuario } from "../usuario/usuario.entity.js";
import { UsuarioService } from "../usuario/usuario.service.js";
import { ReversaoMovimentacaoController } from "./reversao-movimentacao.controller.js";
import { ApiReversaoMovimentacao } from "./reversao-movimentacao.entity.js";
import { ReversaoMovimentacaoService } from "./reversao-movimentacao.service.js";

describe("ReversaoMovimentacaoController", () => {
  let controller: ReversaoMovimentacaoController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ReversaoMovimentacaoController],
      providers: [
        ConfigService,
        EnvService,
        JwtService,
        LimiteCartaoService,
        MovimentacaoService,
        PortadorService,
        ReversaoMovimentacaoService,
        TokenService,
        UnidadeGestoraService,
        UsuarioService,
        { provide: getRepositoryToken(ApiLimiteCartao), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiMovimentacao), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiPortador), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiReversaoMovimentacao), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiUnidadeGestora), useFactory: jest.fn },
        { provide: getRepositoryToken(Usuario), useFactory: jest.fn },
      ],
    }).compile();

    controller = module.get<ReversaoMovimentacaoController>(ReversaoMovimentacaoController);
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });
});
