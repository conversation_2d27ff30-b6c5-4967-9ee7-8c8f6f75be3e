import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { ApiMovimentacao } from "../movimentacao/movimentacao.entity.js";
import { MovimentacaoService } from "../movimentacao/movimentacao.service.js";
import { ApiReversaoMovimentacao } from "./reversao-movimentacao.entity.js";
import { ReversaoMovimentacaoService } from "./reversao-movimentacao.service.js";

describe("ReversaoMovimentacaoService", () => {
  let service: ReversaoMovimentacaoService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MovimentacaoService,
        ReversaoMovimentacaoService,
        { provide: getRepositoryToken(ApiMovimentacao), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiReversaoMovimentacao), useFactory: jest.fn },
      ],
    }).compile();

    service = module.get<ReversaoMovimentacaoService>(ReversaoMovimentacaoService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
