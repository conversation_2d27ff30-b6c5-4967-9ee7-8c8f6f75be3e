import { BadRequestException, Body, Controller, Delete, Get, Param, Post, Req, UseGuards } from "@nestjs/common";
import { assertNotNull } from "cpesc-shared/out/assertions.js";
import { endpoints, type RespostaRota } from "cpesc-shared/out/endpoints/main.js";
import { AuthGuard, type RequisicaoAutenticada } from "../auth/auth.guard.js";
import { ApiReversaoMovimentacao } from "./reversao-movimentacao.entity.js";
import { ReversaoMovimentacaoService } from "./reversao-movimentacao.service.js";

@Controller("reversao-movimentacao")
@UseGuards(AuthGuard)
export class ReversaoMovimentacaoController {
  constructor(private readonly reversaoMovimentacaoService: ReversaoMovimentacaoService) {}

  @Get()
  async findAll() {
    return this.reversaoMovimentacaoService.findAll();
  }

  @Get("/:movimentacaoId")
  async find(@Param("movimentacaoId") movimentacaoId: number) {
    return this.reversaoMovimentacaoService.getReversao(movimentacaoId);
  }

  @Post("/")
  async create(
    @Body() reversao: Partial<ApiReversaoMovimentacao>,
    @Req() req: RequisicaoAutenticada,
  ): RespostaRota<typeof endpoints.criarReversaoMovimentacao> {
    const dataTransacao = new Date(reversao.revertidoEm ?? "");

    if (isNaN(dataTransacao.getTime())) {
      throw new BadRequestException({ sucesso: false, mensagem: "Data Inválida" });
    }

    if (!reversao.movimentacaoId) {
      throw new BadRequestException({ sucesso: false, mensagem: "Movimentação Inválida" });
    }

    if (!reversao.reversaoId) {
      throw new BadRequestException({ sucesso: false, mensagem: "Reversão Inválida" });
    }

    assertNotNull(req.usuario, "Usuário não autenticado");
    const emailUsuario = req.usuario.email;

    const retorno = await this.reversaoMovimentacaoService.salvarReversao(reversao, emailUsuario);
    return { sucesso: true, mensagem: `NF vinculada a Reversão: ${retorno}` };
  }

  @Delete("/:id")
  async remove(@Param("id") id: string): RespostaRota<typeof endpoints.excluirReversaoMovimentacao> {
    await this.reversaoMovimentacaoService.remove(+id);

    return { sucesso: true };
  }
}
