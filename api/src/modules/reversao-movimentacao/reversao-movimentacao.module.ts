import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AuthModule } from "../auth/auth.module.js";
import { PortadorModule } from "../portador/portador.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { UsuarioModule } from "../usuario/usuario.module.js";
import { ApiReversaoMovimentacao } from "./reversao-movimentacao.entity.js";
import { ReversaoMovimentacaoService } from "./reversao-movimentacao.service.js";
import { ReversaoMovimentacaoController } from "./reversao-movimentacao.controller.js";
import { MovimentacaoModule } from "../movimentacao/movimentacao.module.js";

@Module({
  controllers: [ReversaoMovimentacaoController],
  providers: [ReversaoMovimentacaoService],
  imports: [
    TypeOrmModule.forFeature([ApiReversaoMovimentacao]),
    PortadorModule,
    SharedModule,
    AuthModule,
    UsuarioModule,
    MovimentacaoModule,
  ],
  exports: [ReversaoMovimentacaoService],
})
export class ReversaoMovimentacaoModule {}
