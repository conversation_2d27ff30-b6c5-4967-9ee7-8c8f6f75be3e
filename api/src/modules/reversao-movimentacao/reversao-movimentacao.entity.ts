import { ReversaoMovimentacao } from "cpesc-shared";
import {
  <PERSON><PERSON><PERSON>,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { ApiMovimentacao } from "../movimentacao/movimentacao.entity.js";

@Entity("CPESC_REVERSAOMOVIMENTACAO")
export class ApiReversaoMovimentacao implements ReversaoMovimentacao {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "REVERSAO_ID" })
  reversaoId: number;

  @Column({ name: "MOVIMENTACAO_ID" })
  movimentacaoId: number;

  @CreateDateColumn({ name: "REVERTIDOEM" })
  revertidoEm: Date;

  @Column({ name: "CRIADOPOR" })
  criadopor: string;

  @CreateDateColumn({ name: "CRIADOEM" })
  criadoem: Date;

  @Column({ name: "ATUALIZADOPOR" })
  atualizadopor: string;

  @UpdateDateColumn({ name: "ATUALIZADOEM" })
  atualizadoem: Date;

  @OneToOne("ApiMovimentacao")
  @JoinColumn({ name: "REVERSAO_ID" })
  reversao: ApiMovimentacao;

  // eslint-disable-next-line @typescript-eslint/naming-convention -- TODO: padronizar nome
  limitecartao_id?: number | undefined;
}
