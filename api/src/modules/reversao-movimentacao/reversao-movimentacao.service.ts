import { BadRequestException, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { MovimentacaoService } from "../movimentacao/movimentacao.service.js";
import { ApiReversaoMovimentacao } from "./reversao-movimentacao.entity.js";

@Injectable()
export class ReversaoMovimentacaoService {
  constructor(
    @InjectRepository(ApiReversaoMovimentacao)
    private reversaoMovimentacaoRepository: Repository<ApiReversaoMovimentacao>,
    private movimentacaoService: MovimentacaoService,
  ) {}

  async findAll() {
    return this.reversaoMovimentacaoRepository.find();
  }

  async getReversao(movimentacaoId: number): Promise<ApiReversaoMovimentacao[]> {
    return this.reversaoMovimentacaoRepository.find({
      where: [{ movimentacaoId }],
    });
  }

  async salvarReversao(reversao: Partial<ApiReversaoMovimentacao>, emailCriador: string) {
    const reversaoMovimentacao: Partial<ApiReversaoMovimentacao> = {
      movimentacaoId: reversao.movimentacaoId,
      reversaoId: reversao.reversaoId,
      revertidoEm: reversao.revertidoEm,
      criadopor: emailCriador,
      atualizadopor: emailCriador,
    };
    const reversaoNova = this.reversaoMovimentacaoRepository.create(reversaoMovimentacao);
    await this.reversaoMovimentacaoRepository.save(reversaoNova);
    // Atualiza o limitecartao_id da movimentação original e na reversao
    if (reversao.movimentacaoId && reversao.limitecartao_id && reversao.reversaoId) {
      await this.movimentacaoService.associarLimite(
        [reversao.movimentacaoId, reversao.reversaoId],
        reversao.limitecartao_id,
      );

      return reversaoNova.id;
    }

    throw new BadRequestException("Ocorreu um erro ao tentar vincular a Reversão.");
  }

  async remove(id: number) {
    return this.reversaoMovimentacaoRepository.delete({ id });
  }
}
