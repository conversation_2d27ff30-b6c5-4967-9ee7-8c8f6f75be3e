import { jest } from "@jest/globals";
import { BadRequestException } from "@nestjs/common";
import type { TestingModule } from "@nestjs/testing";
import type { endpoints } from "cpesc-shared/out/endpoints/main.js";
import type z from "zod";
import { getMockRequest } from "../../test-utils/mock-request.js";
import { TestModuleBuilder } from "../../test-utils/TestModuleBuilder.js";
import { AuthModule } from "../auth/auth.module.js";
import { ApiCartao } from "../cartao/cartao.entity.js";
import { ApiItemFiscal } from "../item-fiscal/item-fiscal.entity.js";
import { ApiLimiteCartao } from "../limite-cartao/limite-cartao.entity.js";
import { LimiteCartaoModule } from "../limite-cartao/limite-cartao.module.js";
import { ApiMovimentacao } from "../movimentacao/movimentacao.entity.js";
import { MovimentacaoModule } from "../movimentacao/movimentacao.module.js";
import { ApiNotaFiscalItem } from "../nota-fiscal-item/nota-fiscal-item.entity.js";
import { NotaFiscalItemModule } from "../nota-fiscal-item/nota-fiscal-item.module.js";
import { ApiPortador } from "../portador/portador.entity.js";
import { PortadorModule } from "../portador/portador.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { ApiUnidadeGestora } from "../unidade-gestora/unidade-gestora.entity.js";
import { Usuario } from "../usuario/usuario.entity.js";
import { UsuarioModule } from "../usuario/usuario.module.js";
import { NotaFiscalController } from "./nota-fiscal.controller.js";
import { ApiNotaFiscal } from "./nota-fiscal.entity.js";
import { NotaFiscalService } from "./nota-fiscal.service.js";

describe("NotaFiscalController", () => {
  let controller: NotaFiscalController;

  beforeEach(async () => {
    const module: TestingModule = await new TestModuleBuilder()
      .addControllers(NotaFiscalController)
      .addProviders(NotaFiscalService)
      .addImports(
        AuthModule,
        LimiteCartaoModule,
        MovimentacaoModule,
        NotaFiscalItemModule,
        PortadorModule,
        SharedModule,
        UsuarioModule,
      )
      .mockEntities(
        ApiCartao,
        ApiItemFiscal,
        ApiLimiteCartao,
        ApiMovimentacao,
        ApiNotaFiscal,
        ApiNotaFiscalItem,
        ApiPortador,
        ApiUnidadeGestora,
        Usuario,
      )
      .build();

    controller = module.get(NotaFiscalController);
  });

  describe("cria nota fiscal", () => {
    it("retorna sucesso com payload válido", async () => {
      jest.spyOn(NotaFiscalService.prototype, "getNotaFiscalNoSIGEF").mockResolvedValue([
        {
          numero: 1,
          serie: 1,
          numeroserie: "1-1",
          cnpj: "12345678000195",
          chave: "abc",
          valor: 100,
          dataemissao: new Date("2025-01-01T12:00:00Z"),
          codigosigef: 123,
        },
      ]);

      const json = await controller.create(
        {
          chave: "abc",
          cnpj: "12345678000195",
          codigosigef: 123,
          dataemissao: "2025-01-01T12:00:00Z",
          movimentacao_id: 1,
          numero: 1,
          serie: 1,
          tiponotafiscal_id: 1,
          valor: 100,
          desconto: null,
        } satisfies z.infer<typeof endpoints.criarNotaFiscal.tipoPayload>,
        getMockRequest(),
      );
      expect(json.sucesso).toBeTruthy();
    }, 120000);

    it("retorna erro com payload inválido", async () => {
      await expect(async () => controller.create({}, getMockRequest())).rejects.toBeInstanceOf(BadRequestException);
    });
  });
});
