import { NotaFiscal } from "cpesc-shared";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Column,
  CreateDateC<PERSON>umn,
  <PERSON><PERSON>ty,
  <PERSON>in<PERSON><PERSON><PERSON>n,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import type { ApiMovimentacao } from "../movimentacao/movimentacao.entity.js";
import type { ApiNotaFiscalItem } from "../nota-fiscal-item/nota-fiscal-item.entity.js";

@Entity("CPESC_NOTAFISCAL")
export class ApiNotaFiscal implements NotaFiscal {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "MOVIMENTACAO_ID" })
  // eslint-disable-next-line @typescript-eslint/naming-convention -- TODO: padronizar nome
  movimentacao_id: number;

  @Column({ name: "TIPONOTAFISCAL_ID" })
  // eslint-disable-next-line @typescript-eslint/naming-convention -- TODO: padronizar nome
  tiponotafiscal_id: number;

  @Column({ name: "NUMERO" })
  numero: number;

  @Column({ name: "SERIE", nullable: true, type: "number" })
  serie: number | null;

  numeroserie: string;

  @Column({ name: "CNPJ" })
  cnpj: string;

  @Column({ name: "CHAVE", nullable: true, type: String })
  chave: string | null;

  @Column({ name: "VALOR", type: "decimal" })
  valor: number;

  @Column({ name: "DESCONTO", nullable: true, type: "decimal" })
  desconto: number | null;

  @Column({ name: "COFINS", nullable: true, type: "decimal" })
  cofins: number | null;

  @Column({ name: "ISS", nullable: true, type: "decimal" })
  iss: number | null;

  @Column({ name: "PIS", nullable: true, type: "decimal" })
  pis: number | null;

  @Column({ name: "INSS", nullable: true, type: "decimal" })
  inss: number | null;

  @Column({ name: "IR", nullable: true, type: "decimal" })
  ir: number | null;

  @Column({ name: "DATAEMISSAO", nullable: true, type: Date })
  dataemissao: Date | null;

  @Column({ name: "CODIGOSIGEF", nullable: true, type: Number })
  codigosigef: number | null;

  @Column({ name: "CRIADOPOR" })
  criadopor: string;

  @CreateDateColumn({ name: "CRIADOEM" })
  criadoem: Date;

  @Column({ name: "ATUALIZADOPOR" })
  atualizadopor: string;

  @UpdateDateColumn({ name: "ATUALIZADOEM" })
  atualizadoem: Date;

  @OneToMany("ApiNotaFiscalItem", (nfi: ApiNotaFiscalItem) => nfi.notafiscal)
  notafiscalitens: ApiNotaFiscalItem[] | null;

  @OneToOne("ApiMovimentacao", (movimentacao: ApiMovimentacao) => movimentacao.notaFiscal)
  @JoinColumn({ name: "MOVIMENTACAO_ID" })
  movimentacao: ApiMovimentacao;

  @AfterLoad()
  generateFields() {
    this.numeroserie = this.serie ? `${this.numero}-${this.serie}` : `${this.numero}`;
  }
}
