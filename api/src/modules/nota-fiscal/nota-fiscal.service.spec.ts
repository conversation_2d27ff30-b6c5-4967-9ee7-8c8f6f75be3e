import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { ApiItemFiscal } from "../item-fiscal/item-fiscal.entity.js";
import { ItemFiscalService } from "../item-fiscal/item-fiscal.service.js";
import { ApiNotaFiscalItem } from "../nota-fiscal-item/nota-fiscal-item.entity.js";
import { NotaFiscalItemService } from "../nota-fiscal-item/nota-fiscal-item.service.js";
import { ApiNotaFiscal } from "./nota-fiscal.entity.js";
import { NotaFiscalService } from "./nota-fiscal.service.js";

describe("NotaFiscalService", () => {
  let service: NotaFiscalService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ItemFiscalService,
        NotaFiscalItemService,
        NotaFiscalService,
        { provide: getRepositoryToken(ApiItemFiscal), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiNotaFiscal), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiNotaFiscalItem), useFactory: jest.fn },
      ],
    }).compile();

    service = module.get<NotaFiscalService>(NotaFiscalService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
