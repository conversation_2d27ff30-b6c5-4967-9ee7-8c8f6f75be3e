import { forwardRef, Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AuthModule } from "../auth/auth.module.js";
import { NotaFiscalItemModule } from "../nota-fiscal-item/nota-fiscal-item.module.js";
import { PortadorModule } from "../portador/portador.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { UsuarioModule } from "../usuario/usuario.module.js";
import { NotaFiscalController } from "./nota-fiscal.controller.js";
import { ApiNotaFiscal } from "./nota-fiscal.entity.js";
import { NotaFiscalService } from "./nota-fiscal.service.js";

@Module({
  imports: [
    TypeOrmModule.forFeature([ApiNotaFiscal]),
    forwardRef(() => PortadorModule),
    SharedModule,
    forwardRef(() => AuthModule),
    NotaFiscalItemModule,
    UsuarioModule,
  ],
  controllers: [NotaFiscalController],
  providers: [NotaFiscalService],
  exports: [NotaFiscalService],
})
export class NotaFiscalModule {}
