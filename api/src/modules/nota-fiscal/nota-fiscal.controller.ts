import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpException,
  HttpStatus,
  InternalServerErrorException,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from "@nestjs/common";
import { assertNotNull } from "cpesc-shared/out/assertions.js";
import { endpoints, type RespostaRota } from "cpesc-shared/out/endpoints/main.js";
import type { PayloadCriarNFImportacao, PayloadCriarNFManual } from "cpesc-shared/out/endpoints/nota-fiscal.js";
import type { FindOptionsWhere } from "typeorm";
import { validarPayload } from "../../helpers/validar-payload.js";
import { AuthGuard, type RequisicaoAutenticada } from "../auth/auth.guard.js";
import { ApiNotaFiscal } from "./nota-fiscal.entity.js";
import { NotaFiscalService } from "./nota-fiscal.service.js";

@Controller("nota-fiscal")
@UseGuards(AuthGuard)
export class NotaFiscalController {
  constructor(private readonly notaFiscalService: NotaFiscalService) {}

  @Post("/")
  async create(
    @Body() payload: unknown,
    @Req() req: RequisicaoAutenticada,
  ): RespostaRota<typeof endpoints.criarNotaFiscal> {
    try {
      const nota = validarPayload(payload, endpoints.criarNotaFiscal);
      const dataEmissao = new Date(nota.dataemissao);

      if (isNaN(dataEmissao.getTime())) {
        throw new BadRequestException({ sucesso: false, mensagem: "Data Inválida" });
      }

      assertNotNull(req.usuario, "Usuário não autenticado");
      const emailUsuario = req.usuario.email;
      let idNotaFiscalGerada: number | undefined;

      switch (nota.tiponotafiscal_id) {
        case 1: {
          const notaImportacao = nota as PayloadCriarNFImportacao;

          if (!notaImportacao.codigosigef) {
            throw new BadRequestException({ sucesso: false, mensagem: "Código SIGEF Inválido" });
          }
          if (!notaImportacao.chave) {
            throw new BadRequestException({ sucesso: false, mensagem: "Nota Fiscal importada requer chave" });
          }

          idNotaFiscalGerada = await this.notaFiscalService.vincularNotaFiscalImportacao(notaImportacao, emailUsuario);
          break;
        }
        case 2:
        case 3:
        case 4: {
          const notaManual = nota as PayloadCriarNFManual;

          if (!notaManual.notafiscalitens || notaManual.notafiscalitens.length === 0) {
            throw new BadRequestException({ sucesso: false, mensagem: "Nota Fiscal sem itens válidos" });
          }

          idNotaFiscalGerada = await this.notaFiscalService.criarNotaFiscalManualComItens(
            notaManual,
            dataEmissao,
            emailUsuario,
          );
          break;
        }
        default:
          throw new BadRequestException({ sucesso: false, mensagem: "Tipo de Nota Fiscal Inválido" });
      }

      if (!idNotaFiscalGerada) {
        throw new InternalServerErrorException({ sucesso: false, mensagem: "Erro ao criar Nota Fiscal" });
      }

      return { sucesso: true, mensagem: "NF criada", idNotaFiscalGerada };
    } catch (err) {
      if (err instanceof HttpException) {
        throw err;
      }

      const error = err as Error;
      throw new InternalServerErrorException({
        sucesso: false,
        mensagem: error.message,
      });
    }
  }

  @Get()
  async findAll(@Query() query: FindOptionsWhere<ApiNotaFiscal>): RespostaRota<typeof endpoints.buscarNotasFiscais> {
    return this.notaFiscalService.findAll(query);
  }

  @Get("/:id")
  async findBy(@Param("id") id: string) {
    const idNumber = parseInt(id);
    if (isNaN(idNumber)) {
      throw new BadRequestException("ID inválido");
    }
    return this.notaFiscalService.getPorId(idNumber);
  }

  @Put("/:id")
  async update(
    @Req() req: RequisicaoAutenticada,
    @Param("id") id: string,
    @Body() notaFiscal: Partial<ApiNotaFiscal>,
  ): RespostaRota<typeof endpoints.editarNotaFiscal> {
    const idNumber = parseInt(id);

    if (isNaN(idNumber)) {
      throw new BadRequestException("ID inválido");
    }

    const dataEmissao = new Date(notaFiscal.dataemissao ?? "");

    if (isNaN(dataEmissao.getTime())) {
      throw new BadRequestException("Data Inválida");
    }

    notaFiscal.dataemissao = dataEmissao;
    assertNotNull(req.usuario, "Usuário não autenticado");
    await this.notaFiscalService.atualizarNotaFiscal(idNumber, req.usuario.email, notaFiscal);

    return { sucesso: true, mensagem: "NF atualizada" };
  }

  @HttpCode(HttpStatus.OK)
  @Get("/buscar-externo/:parametro")
  async findNotaFiscalNoSIGEF(
    @Param("parametro") param: string,
  ): RespostaRota<typeof endpoints.buscarNotasFiscaisExternas> {
    return this.notaFiscalService.getNotaFiscalNoSIGEF(param);
  }

  @Delete(":id")
  async remove(@Param("id") id: string) {
    return this.notaFiscalService.removeNotaFiscalENotaFiscalItem(+id);
  }
}
