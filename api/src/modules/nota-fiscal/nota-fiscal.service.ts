import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { NotaFiscalSigef } from "cpesc-shared";
import { PayloadCriarNFItem } from "cpesc-shared/out/endpoints/nota-fiscal-item.js";
import type { PayloadCriarNFImportacao, PayloadCriarNFManual } from "cpesc-shared/out/endpoints/nota-fiscal.js";
import { DataSource, FindOptionsWhere, Repository, UpdateResult } from "typeorm";
import { NotaFiscalItemService } from "../nota-fiscal-item/nota-fiscal-item.service.js";
import { ApiNotaFiscal } from "./nota-fiscal.entity.js";

@Injectable()
export class NotaFiscalService {
  constructor(
    @InjectRepository(ApiNotaFiscal) private notaFiscalRepository: Repository<ApiNotaFiscal>,
    private nfiService: NotaFiscalItemService,
    private dataSource: DataSource,
  ) {}

  async criarNotaFiscalManualComItens(
    dados: PayloadCriarNFManual,
    dataEmissao: Date,
    emailCriador: string,
  ): Promise<number | undefined> {
    try {
      const notaFiscal: Omit<
        ApiNotaFiscal,
        "id" | "numeroserie" | "notafiscalitens" | "movimentacao" | "generateFields"
      > = {
        movimentacao_id: dados.movimentacao_id,
        tiponotafiscal_id: dados.tiponotafiscal_id,
        numero: dados.numero,
        serie: dados.serie,
        cnpj: dados.cnpj,
        chave: null,
        valor: dados.valor,
        codigosigef: null,
        desconto: dados.desconto ?? null,
        cofins: dados.cofins ?? null,
        iss: dados.iss ?? null,
        pis: dados.pis ?? null,
        inss: dados.inss ?? null,
        ir: dados.ir ?? null,
        dataemissao: dataEmissao,
        criadopor: emailCriador,
        criadoem: new Date(),
        atualizadopor: emailCriador,
        atualizadoem: new Date(),
      };
      const nf = await this.notaFiscalRepository.save(notaFiscal);
      const itens = dados.notafiscalitens;
      if (itens) {
        await this.nfiService.criarItemManual(itens as PayloadCriarNFItem[], nf.id, emailCriador);
      }

      return nf.id;
    } catch (error) {
      console.log("Erro ao salvar a nota fiscal", error);
      return undefined;
    }
  }

  async vincularNotaFiscalImportacao(
    dados: PayloadCriarNFImportacao,
    emailCriador: string,
  ): Promise<number | undefined> {
    try {
      if (!dados.chave) {
        console.log("Nota fiscal sem chave.");
        return undefined;
      }

      const listaNotasSigef = await this.getNotaFiscalNoSIGEF(dados.chave);

      if (listaNotasSigef.length === 0) {
        console.log("Nota fiscal não encontrada com a chave:", dados.chave);
        return undefined;
      }

      const notaSigef = listaNotasSigef[0];
      const notaFiscal: Omit<
        ApiNotaFiscal,
        | "id"
        | "numeroserie"
        | "notafiscalitens"
        | "desconto"
        | "cofins"
        | "iss"
        | "pis"
        | "inss"
        | "ir"
        | "movimentacao"
        | "generateFields"
      > = {
        movimentacao_id: dados.movimentacao_id,
        tiponotafiscal_id: dados.tiponotafiscal_id,
        numero: notaSigef.numero,
        serie: notaSigef.serie,
        cnpj: notaSigef.cnpj,
        chave: notaSigef.chave,
        valor: notaSigef.valor,
        dataemissao: notaSigef.dataemissao,
        codigosigef: notaSigef.codigosigef,
        criadopor: emailCriador,
        criadoem: new Date(),
        atualizadopor: emailCriador,
        atualizadoem: new Date(),
      };

      const nf = await this.notaFiscalRepository.save(notaFiscal);
      await this.nfiService.criarItemBuscandoSigef(dados.codigosigef, nf.id, emailCriador);

      return nf.id;
    } catch (error) {
      console.log("Erro ao buscar nota fiscal por chave", error);
      return undefined;
    }
  }

  async atualizarNotaFiscal(
    id: number,
    emailUsuario: string,
    notaFiscal: Partial<ApiNotaFiscal>,
  ): Promise<UpdateResult | undefined> {
    try {
      notaFiscal.atualizadopor = emailUsuario;
      notaFiscal.atualizadoem = new Date();
      const { notafiscalitens, ...notaFiscalSemItens } = notaFiscal;
      const nfAtualizada = await this.notaFiscalRepository.update(id, notaFiscalSemItens);
      await this.nfiService.atualizarItem(notafiscalitens ?? [], id, emailUsuario);

      return nfAtualizada;
    } catch (error) {
      console.log("Erro ao atualizar a nota fiscal", error);
      return undefined;
    }
  }

  async getPorId(id: number): Promise<ApiNotaFiscal | null> {
    return this.notaFiscalRepository.findOneBy({ id });
  }

  async findAll(params: FindOptionsWhere<ApiNotaFiscal>): Promise<ApiNotaFiscal[]> {
    return this.notaFiscalRepository.find({
      where: params,
      relations: { notafiscalitens: { itemfiscal: true } },
    });
  }

  async removeNotaFiscalENotaFiscalItem(idRemover: number) {
    await this.nfiService.removeByNotaFiscalId(idRemover);
    return this.notaFiscalRepository.delete({ id: idRemover });
  }

  async getNotaFiscalNoSIGEF(parametro: string): Promise<NotaFiscalSigef[]> {
    let query: string;
    let params: string[];

    const baseQuery = `SELECT
          S.NUDOCNFE AS NUMERO,
          S.NUSERIENFE AS SERIE,
          S.NUDOCNFE || '-'|| S.NUSERIENFE AS NUMEROSERIE,
          S.NUCNPJEMITENTE AS CNPJ,
          S.NUCHAVEACESSO AS CHAVE,
          S.VLTOTALNOTAFISCAL AS VALOR,
          S.DTEMISSAO AS DATAEMISSAO,
          S.NUNFEID AS CODIGOSIGEF
         FROM SIGEF.EFINNOTAFISCALSAT@SIGEFCPESC S`;

    if (parametro.includes("-")) {
      const listaparametros = parametro.split("-");
      query = `${baseQuery} WHERE S.NUCNPJEMITENTE = :p1 AND S.NUDOCNFE = :p2 AND S.NUSERIENFE = :p3`;
      params = listaparametros;
    } else {
      query = `${baseQuery} WHERE S.NUCHAVEACESSO = :pchave`;
      params = [parametro];
    }

    const resultado = await this.dataSource.query<
      {
        NUMERO: number;
        SERIE: number;
        NUMEROSERIE: string;
        CNPJ: string;
        CHAVE: string;
        VALOR: number;
        DATAEMISSAO: string;
        CODIGOSIGEF: number;
      }[]
    >(query, params);

    return resultado.map(element => ({
      numero: element.NUMERO,
      serie: element.SERIE,
      numeroserie: element.NUMEROSERIE,
      cnpj: element.CNPJ,
      chave: element.CHAVE,
      valor: element.VALOR,
      dataemissao: new Date(element.DATAEMISSAO),
      codigosigef: element.CODIGOSIGEF,
    }));
  }
}
