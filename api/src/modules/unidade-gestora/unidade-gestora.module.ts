import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AuthModule } from "../auth/auth.module.js";
import { PortadorModule } from "../portador/portador.module.js";
import { UsuarioModule } from "../usuario/usuario.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { UnidadeGestoraController } from "./unidade-gestora.controller.js";
import { ApiUnidadeGestora } from "./unidade-gestora.entity.js";
import { UnidadeGestoraService } from "./unidade-gestora.service.js";

@Module({
  imports: [TypeOrmModule.forFeature([ApiUnidadeGestora]), SharedModule, UsuarioModule, PortadorModule, AuthModule],
  controllers: [UnidadeGestoraController],
  providers: [UnidadeGestoraService],
  exports: [UnidadeGestoraService],
})
export class UnidadeGestoraModule {}
