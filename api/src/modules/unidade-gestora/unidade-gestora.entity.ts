import { UnidadeGestora } from "cpesc-shared";
import {
  <PERSON><PERSON>n,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from "typeorm";
import type { ApiContaBanco } from "../conta-banco/conta-banco.entity.js";
import type { ApiLimiteCartao } from "../limite-cartao/limite-cartao.entity.js";
import type { ApiMunicipio } from "../municipio/municipio.entity.js";
import { ApiUnidadeAdministrativa } from "../unidade-administrativa/unidade-administrativa.entity.js";

@Entity("CPESC_UNIDADEGESTORA")
export class ApiUnidadeGestora implements UnidadeGestora {
  @PrimaryColumn({ name: "ID" })
  id: number;

  @Column({ name: "CODIGO" })
  codigo: number;

  @Column({ name: "CODIGOGESTAO" })
  codigoGestao: number;

  @Column({ name: "NOME" })
  nome: string;

  @Column({ name: "NOMEORDENADOR" })
  nomeOrdenador: string;

  @Column({ name: "CNPJ" })
  cnpj: string;

  @Column({ name: "NOMELOGRADOURO" })
  logradouro: string;

  @Column({ name: "NUMEROLOGRADOURO" })
  numero: string;

  @Column({ name: "COMPLEMENTO" })
  complemento: string;

  @Column({ name: "BAIRRO" })
  bairro: string;

  @Column({ name: "CIDADE" })
  cidade: string;

  @Column({ name: "CEP" })
  cep: string;

  @Column({ name: "DDD" })
  ddd: string;

  @Column({ name: "FONE" })
  telefone: string;

  @Column({ name: "NOMEABREVIADO" })
  nomeAbreviado: string;

  @Column({ name: "DESCRICAO" })
  descricao: string;

  @Column({ name: "BLOQUEIO_ID" })
  bloqueado: number;

  @Column({ name: "ADICIONARITEMPC" })
  permiteAdicionar: number;

  @Column({ name: "RATEIO" })
  calcularPagamento: number;

  @Column({ name: "ATIVO" })
  ativo: string;

  @Column({ name: "SIGLASGPE" })
  siglasGpe: string;

  @Column({ name: "LTCPFAIGNORAR" })
  cpfIgnorar: string;

  @Column({ name: "CRIADOPOR" })
  criadopor: string;

  @CreateDateColumn({ name: "CRIADOEM" })
  criadoem: Date;

  @Column({ name: "ATUALIZADOPOR" })
  atualizadopor: string;

  @UpdateDateColumn({ name: "ATUALIZADOEM" })
  atualizadoem: Date;

  @ManyToOne("ApiMunicipio")
  @JoinColumn({ name: "MUNICIPIO_ID" })
  municipio: ApiMunicipio;

  @OneToMany("ApiLimiteCartao", (lc: ApiLimiteCartao) => lc.unidadeGestora)
  limites: ApiLimiteCartao[];

  @OneToOne("ApiContaBanco", (cb: ApiContaBanco) => cb.unidadeGestora)
  contaBanco: ApiContaBanco;

  @OneToMany("ApiUnidadeAdministrativa", (ua: ApiUnidadeAdministrativa) => ua.unidadeGestora)
  unidadeAdministrativa: ApiUnidadeAdministrativa[];
}
