import { ClassSerializerInterceptor, Controller, Get, Query, Req, UseGuards, UseInterceptors } from "@nestjs/common";
import { ParametrosBuscaGastos, RelatoriosService } from "./relatorios.service.js";
import { AuthGuard, RequisicaoAutenticada } from "../auth/auth.guard.js";

@Controller("relatorios")
@UseGuards(AuthGuard)
export class RelatoriosController {
  constructor(private readonly relatoriosService: RelatoriosService) {}

  @UseInterceptors(ClassSerializerInterceptor)
  @Get("gastos")
  async findGastosProduto(@Req() req: RequisicaoAutenticada, @Query() params: ParametrosBuscaGastos) {
    return this.relatoriosService.getGastosProdutos(params, req);
  }
}
