import { jest } from "@jest/globals";
import { ConfigService } from "@nestjs/config";
import { JwtService } from "@nestjs/jwt";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { TokenService } from "../../helpers/token.service.js";
import { ApiCartao } from "../cartao/cartao.entity.js";
import { CartaoService } from "../cartao/cartao.service.js";
import { EmailService } from "../email/email.service.js";
import { ApiLimiteCartao } from "../limite-cartao/limite-cartao.entity.js";
import { LimiteCartaoService } from "../limite-cartao/limite-cartao.service.js";
import { ApiMovimentacao } from "../movimentacao/movimentacao.entity.js";
import { MovimentacaoService } from "../movimentacao/movimentacao.service.js";
import { ApiPortador } from "../portador/portador.entity.js";
import { PortadorService } from "../portador/portador.service.js";
import { EnvService } from "../shared/env.service.js";
import { ApiUnidadeGestora } from "../unidade-gestora/unidade-gestora.entity.js";
import { UnidadeGestoraService } from "../unidade-gestora/unidade-gestora.service.js";
import { Usuario } from "../usuario/usuario.entity.js";
import { UsuarioService } from "../usuario/usuario.service.js";
import { AuthController } from "./auth.controller.js";
import { AuthGuard } from "./auth.guard.js";
import { AuthService } from "./auth.service.js";

describe("AuthController", () => {
  let controller: AuthController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        AuthGuard,
        AuthService,
        CartaoService,
        ConfigService,
        EmailService,
        EnvService,
        JwtService,
        LimiteCartaoService,
        MovimentacaoService,
        PortadorService,
        TokenService,
        UnidadeGestoraService,
        UsuarioService,
        { provide: getRepositoryToken(ApiCartao), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiLimiteCartao), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiMovimentacao), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiPortador), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiUnidadeGestora), useFactory: jest.fn },
        { provide: getRepositoryToken(Usuario), useFactory: jest.fn },
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });
});
