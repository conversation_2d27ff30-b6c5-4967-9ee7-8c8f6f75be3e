import { forwardRef, <PERSON>du<PERSON> } from "@nestjs/common";
import { JwtModule } from "@nestjs/jwt";
import type { Ambiente } from "../../helpers/Ambiente.js";
import { TokenService } from "../../helpers/token.service.js";
import { CartaoModule } from "../cartao/cartao.module.js";
import { EmailModule } from "../email/email.module.js";
import { PortadorModule } from "../portador/portador.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { UsuarioModule } from "../usuario/usuario.module.js";
import { AuthController } from "./auth.controller.js";
import { AuthGuard } from "./auth.guard.js";
import { AuthService } from "./auth.service.js";

const env = process.env as unknown as Ambiente;

@Module({
  imports: [
    JwtModule.register({
      global: true,
      secret: env.JWT_SECRET,
      signOptions: { expiresIn: env.JWT_EXPIRATION_TIME },
    }),
    SharedModule,
    UsuarioModule,
    EmailModule,
    forwardRef(() => PortadorModule),
    forwardRef(() => CartaoModule),
  ],
  controllers: [AuthController],
  providers: [AuthGuard, AuthService, TokenService],
  exports: [TokenService, AuthGuard],
})
export class AuthModule {}
