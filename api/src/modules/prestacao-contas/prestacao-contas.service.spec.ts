import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { ApiMovimentacao } from "../movimentacao/movimentacao.entity.js";
import { MovimentacaoService } from "../movimentacao/movimentacao.service.js";
import { ApiPrestacaoContas } from "./prestacao-contas.entity.js";
import { PrestacaoContasService } from "./prestacao-contas.service.js";

describe("PrestacaoContasService", () => {
  let service: PrestacaoContasService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MovimentacaoService,
        PrestacaoContasService,
        { provide: getRepositoryToken(ApiMovimentacao), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiPrestacaoContas), useFactory: jest.fn },
      ],
    }).compile();

    service = module.get<PrestacaoContasService>(PrestacaoContasService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
