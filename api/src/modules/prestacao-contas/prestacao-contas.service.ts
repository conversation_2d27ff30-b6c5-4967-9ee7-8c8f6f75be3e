import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { SituacaoPrestacaoConta } from "cpesc-shared";
import { Repository } from "typeorm";
import { MovimentacaoService } from "../movimentacao/movimentacao.service.js";
import { ApiPrestacaoContas } from "./prestacao-contas.entity.js";

@Injectable()
export class PrestacaoContasService {
  constructor(
    @InjectRepository(ApiPrestacaoContas) private prestacaoContasRepository: Repository<ApiPrestacaoContas>,
    private movimentacaoService: MovimentacaoService,
  ) {}

  async getPrestacaoContaAtiva(id: number): Promise<ApiPrestacaoContas[]> {
    return this.prestacaoContasRepository.find({
      where: {
        limite: { id },
        situacao: SituacaoPrestacaoConta.Realizada,
      },
      relations: { limite: true },
    });
  }

  async getPrestacoesContas(): Promise<ApiPrestacaoContas[]> {
    return this.prestacaoContasRepository.find({});
  }

  async getPrestacaoContasPorId(id: number): Promise<ApiPrestacaoContas | null> {
    return this.prestacaoContasRepository.findOne({ where: { id } });
  }

  async setPrestacaoContas(prestacaoContas: ApiPrestacaoContas): Promise<void> {
    await this.prestacaoContasRepository.save(prestacaoContas);
  }

  async criarPrestacaoContas(
    limiteCartaoId: number,
    valorGasto: number,
    orgaoSgpe: string,
    processoSgpe: string,
    anoSgpe: string,
    updatedBy: string,
    createdBy: string,
    ip: string,
  ): Promise<ApiPrestacaoContas> {
    // Monta o objeto com os dados recebidos como parâmetros

    const dados: Partial<ApiPrestacaoContas> = {
      limiteCartaoId,
      valorGasto,
      orgaoSgpe,
      processoSgpe,
      anoSgpe,
      updatedBy,
      createdBy,
      situacao: SituacaoPrestacaoConta.Realizada,
      ip,
    };
    const pc = this.prestacaoContasRepository.create(dados);
    return this.prestacaoContasRepository.save(pc);
  }

  async cancelarPrestacao(
    prestacaoId: number,
    motivoCancelamento: string,
    dataCancelamento: Date,
    canceladoPor: string,
    ipCancelamento: string,
  ): Promise<boolean> {
    const prestacao: ApiPrestacaoContas | null = await this.prestacaoContasRepository.findOneBy({ id: prestacaoId });
    if (prestacao != null) {
      const limiteCartaoId = prestacao.limiteCartaoId;
      const removerMovimentacoesVinculados =
        await this.movimentacaoService.removerMovimentacoesVinculados(limiteCartaoId);
      if (!removerMovimentacoesVinculados) {
        return false;
      }
      prestacao.situacao = SituacaoPrestacaoConta.Cancelada;
      prestacao.canceladoEm = dataCancelamento;
      prestacao.canceladoPor = canceladoPor;
      prestacao.ipCancelamento = ipCancelamento;
      prestacao.motivoCancelamento = motivoCancelamento;
      await this.prestacaoContasRepository.save(prestacao);
      return true;
    }
    return false;
  }
}
