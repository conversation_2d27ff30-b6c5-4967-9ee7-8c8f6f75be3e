import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AuthModule } from "../auth/auth.module.js";
import { PortadorModule } from "../portador/portador.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { UsuarioModule } from "../usuario/usuario.module.js";
import { PrestacaoContasController } from "./prestacao-contas.controller.js";
import { ApiPrestacaoContas } from "./prestacao-contas.entity.js";
import { PrestacaoContasService } from "./prestacao-contas.service.js";
import { MovimentacaoModule } from "../movimentacao/movimentacao.module.js";
@Module({
  imports: [
    TypeOrmModule.forFeature([ApiPrestacaoContas]),
    SharedModule,
    UsuarioModule,
    PortadorModule,
    AuthModule,
    MovimentacaoModule,
  ],
  controllers: [PrestacaoContasController],
  providers: [PrestacaoContasService],
  exports: [PrestacaoContasService],
})
export class PrestacaoContasModule {}
