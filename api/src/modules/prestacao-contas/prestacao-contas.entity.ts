import { PrestacaoContas, SituacaoPrestacaoConta } from "cpesc-shared";
import {
  <PERSON>umn,
  CreateDateColumn,
  <PERSON>tity,
  <PERSON>in<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import type { ApiLimiteCartao } from "../limite-cartao/limite-cartao.entity.js";

@Entity("CPESC_PRESTACAOCONTAS")
export class ApiPrestacaoContas implements PrestacaoContas {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "LIMITECARTAO_ID" })
  limiteCartaoId: number;

  @Column({ name: "VALORGASTO" })
  valorGasto: number;

  @Column({ name: "ORGAOSGPE" })
  orgaoSgpe: string;

  @Column({ name: "PROCESSOSGPE" })
  processoSgpe: string;

  @Column({ name: "ANOSGPE" })
  anoSgpe: string;

  @CreateDateColumn({ name: "CRIADOEM" })
  createdAt: Date;

  @Column({ name: "CRIADOPOR" })
  createdBy: string;

  @UpdateDateColumn({ name: "ATUALIZADOEM" })
  updatedAt: Date;

  @Column({ name: "ATUALIZADOPOR" })
  updatedBy: string;

  @Column({ name: "IP" })
  ip: string;

  @Column({ name: "SITUACAO" })
  situacao: SituacaoPrestacaoConta;

  @Column({ name: "CANCELADOEM" })
  canceladoEm: Date;

  @Column({ name: "CANCELADOPOR" })
  canceladoPor: string;

  @Column({ name: "IPCANCELAMENTO" })
  ipCancelamento: string;

  @Column({ name: "MOTIVOCANCELAMENTO" })
  motivoCancelamento: string;

  @ManyToOne("ApiLimiteCartao", (l: ApiLimiteCartao) => l.id)
  @JoinColumn({ name: "LIMITECARTAO_ID" })
  limite: ApiLimiteCartao;
}
