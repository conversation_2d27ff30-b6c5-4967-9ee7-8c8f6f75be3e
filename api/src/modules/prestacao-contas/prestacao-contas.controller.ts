import { Body, Controller, Get, Param, Patch, Post, Req, UseGuards, HttpException, HttpStatus } from "@nestjs/common";
import { endpoints, type RespostaRota } from "cpesc-shared/out/endpoints/main.js";
import { AuthGuard, type RequisicaoAutenticada } from "../auth/auth.guard.js";
import { PrestacaoContasService } from "./prestacao-contas.service.js";

@Controller("prestacao-contas")
@UseGuards(AuthGuard)
export class PrestacaoContasController {
  constructor(private readonly prestacaoContasService: PrestacaoContasService) {}

  @Post("/")
  async createPrestacaoContas(
    @Body()
    body: {
      limiteCartaoId: number;
      valorGasto: number;
      orgaoSgpe: string;
      processoSgpe: string;
      anoSgpe: string;
    },
    @Req() req: RequisicaoAutenticada,
  ): RespostaRota<typeof endpoints.criarPrestacaoContas> {
    const ip = req.usuario?.ipAddress;
    const updatedBy = req.usuario?.email;
    const createdBy = req.usuario?.email;

    return this.prestacaoContasService.criarPrestacaoContas(
      body.limiteCartaoId,
      body.valorGasto,
      body.orgaoSgpe,
      body.processoSgpe,
      body.anoSgpe,
      updatedBy ?? "erro no sistema de login",
      createdBy ?? "erro no sistema de login",
      ip ?? "erro no sistema de ip de login",
    );
  }

  @Get()
  async findAll() {
    return this.prestacaoContasService.getPrestacoesContas();
  }

  @Get("/:id")
  async findPrestacaoContasPorId(@Param("id") id: string) {
    return this.prestacaoContasService.getPrestacaoContasPorId(+id);
  }

  @Get("/ativa/:id")
  async findPrestacaoContaAtiva(@Param("id") id: string) {
    return this.prestacaoContasService.getPrestacaoContaAtiva(+id);
  }

  @Patch("/")
  async cancelarPrestacao(
    @Body()
    body: {
      prestacaoId: number;
      motivoCancelamento: string;
    },
    @Req() req: RequisicaoAutenticada,
  ): RespostaRota<typeof endpoints.cancelarPrestacaoContas> {
    const dataCancelamento = new Date();
    const ipCancelamento = req.usuario?.ipAddress;
    const canceladoPor = req.usuario?.email;

    const sucesso = await this.prestacaoContasService.cancelarPrestacao(
      body.prestacaoId,
      body.motivoCancelamento,
      dataCancelamento,
      canceladoPor ?? "erro no sistema de login",
      ipCancelamento ?? "erro no sistema de ip de login",
    );

    if (!sucesso) {
      throw new HttpException("Falha ao cancelar prestação de contas.", HttpStatus.EXPECTATION_FAILED);
    }

    return sucesso;
  }
}
