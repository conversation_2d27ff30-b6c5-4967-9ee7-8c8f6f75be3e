import type { MigrationInterface, QueryRunner } from "typeorm";

/* 
ALTER TABLE PORTADORCARTAO ADD (
  codigo_otp VARCHAR2(255),
  otp_expiration TIMESTAMP
);
 */
export class UpdatePortadores1715033716105 implements MigrationInterface {
  name = "UpdatePortadores1715033716105";

  async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "PORTADORCARTAO" ADD "codigo_otp" character varying`);
    await queryRunner.query(`ALTER TABLE "PORTADORCARTAO" ADD "otp_expiration" TIMESTAMP`);
  }

  async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "PORTADORCARTAO" DROP COLUMN "otp_expiration"`);
    await queryRunner.query(`ALTER TABLE "PORTADORCARTAO" DROP COLUMN "codigo_otp"`);
  }
}
