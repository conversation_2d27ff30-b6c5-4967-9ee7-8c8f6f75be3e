import type { MigrationInterface, QueryRunner } from "typeorm";

export class CriarTabelaUsuario1715033716104 implements MigrationInterface {
  name = "CriarTabelaUsuario1715033716104";

  async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "usuario" (
        "id" SERIAL NOT NULL PRIMARY KEY,
        "nome" character varying NOT NULL,
        "email" character varying NOT NULL
      )`,
    );
  }

  async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TABLE "usuario"');
  }
}
