import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AppController } from "./app.controller.js";
import { AppService } from "./app.service.js";
import { AuthModule } from "./modules/auth/auth.module.js";
import { CartaoModule } from "./modules/cartao/cartao.module.js";
import { CentroCustoModule } from "./modules/centro-custo/centro-custo.module.js";
import { ContaBancoModule } from "./modules/conta-banco/conta-banco.module.js";
import { LimiteCartaoModule } from "./modules/limite-cartao/limite-cartao.module.js";
import { MovimentacaoModule } from "./modules/movimentacao/movimentacao.module.js";
import { MunicipioModule } from "./modules/municipio/municipio.module.js";
import { NotaFiscalItemModule } from "./modules/nota-fiscal-item/nota-fiscal-item.module.js";
import { NotaFiscalModule } from "./modules/nota-fiscal/nota-fiscal.module.js";
import { OrgaosModule } from "./modules/orgaos/orgaos.module.js";
import { PortadorModule } from "./modules/portador/portador.module.js";
import { PrestacaoContasModule } from "./modules/prestacao-contas/prestacao-contas.module.js";
import { SharedModule } from "./modules/shared/shared.module.js";
import { SubelementoModule } from "./modules/subelemento/subelemento.module.js";
import { UnidadeAdministrativaModule } from "./modules/unidade-administrativa/unidade-administrativa.module.js";
import { UnidadeGestoraModule } from "./modules/unidade-gestora/unidade-gestora.module.js";
import { UsuarioModule } from "./modules/usuario/usuario.module.js";
import { ormconfig } from "./ormconfig.js";
import { ItemFiscalModule } from "./modules/item-fiscal/item-fiscal.module.js";
import { ReversaoMovimentacaoModule } from "./modules/reversao-movimentacao/reversao-movimentacao.module.js";
import { RelatoriosModule } from "./modules/relatorios/relatorios.module.js";

@Module({
  imports: [
    AuthModule,
    SharedModule,
    TypeOrmModule.forRoot(ormconfig),
    UsuarioModule,
    PortadorModule,
    OrgaosModule,
    CentroCustoModule,
    UnidadeGestoraModule,
    ContaBancoModule,
    MunicipioModule,
    UnidadeAdministrativaModule,
    CartaoModule,
    LimiteCartaoModule,
    SubelementoModule,
    NotaFiscalModule,
    NotaFiscalItemModule,
    MovimentacaoModule,
    PrestacaoContasModule,
    ItemFiscalModule,
    ReversaoMovimentacaoModule,
    RelatoriosModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
