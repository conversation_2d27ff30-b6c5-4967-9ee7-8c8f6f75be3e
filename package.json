{"name": "cpesc", "private": true, "version": "1.0.0", "description": "Nova versão do CPESC com foco em monitoramento e operação", "workspaces": ["api", "frontend", "shared"], "scripts": {"okd:hom": "npm --prefix shared run build && npm --prefix api run build && npm --prefix frontend run build:hom", "preinstall": "node ./skip-preinstall.js || (rm -f package-lock.json api/package-lock.json frontend/package-lock.json shared/package-lock.json)", "postinstall": "node ./skip-install.js || (npm run install:api && npm run install:frontend && npm run install:shared)", "install:api": "npm --prefix api install", "install:frontend": "npm --prefix frontend install", "install:shared": "npm --prefix shared install", "start": "sh start.sh", "start:api": "npm --prefix api start", "start:frontend": "npm --prefix frontend start", "start:shared": "npm --prefix shared start", "build": "tsc -b", "build:api": "npm --prefix api run build", "build:frontend": "npm --prefix frontend run build", "build:shared": "npm --prefix shared run build", "build:watch": "tsc -b -w", "version": "zsc version", "flow:feature": "zsc feature", "flow:hotfix": "zsc hotfix", "flow:release": "zsc release", "migrate": "zsc migrate", "migrate:status": "zsc migrate status", "migrate:rollback": "zsc migrate rollback", "migrate:new": "zsc migrate new", "lint": "npm run lint:check", "lint:check": "npm run prettier:check && npm run eslint:check", "lint:check:api": "npm run prettier:check:api && npm run eslint:check:api", "lint:check:frontend": "npm run prettier:check:frontend && npm run eslint:check:frontend", "eslint:check": "NODE_OPTIONS=--max-old-space-size=8192 eslint . --max-warnings 0", "eslint:check:api": "NODE_OPTIONS=--max-old-space-size=8192 eslint api --max-warnings 0", "eslint:check:frontend": "NODE_OPTIONS=--max-old-space-size=8192 eslint frontend --max-warnings 0", "eslint:fix": "npm run eslint:fix:api && npm run eslint:fix:frontend", "eslint:fix:api": "eslint api --fix", "eslint:fix:frontend": "eslint frontend --fix", "prettier:check": "npm run prettier:check:api && npm run prettier:check:frontend", "prettier:check:api": "prettier --check api", "prettier:check:frontend": "prettier --check frontend", "prettier:fix": "npm run prettier:fix:api && npm run prettier:fix:frontend", "prettier:fix:api": "prettier --write api", "prettier:fix:frontend": "prettier --write frontend", "test:api": "npm --prefix api run test"}, "devDependencies": {"@ghabriel/z-script": "^1.1.2", "@stylistic/eslint-plugin": "^5.2.1", "@types/node": "^22.15.18", "concurrently": "^9.2.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "nodemon": "^3.1.10", "prettier": "^3.6.2", "typescript": "^5.8.3", "typescript-eslint": "^8.37.0"}, "engines": {"node": ">= 20.19.0"}}